#!/usr/bin/env python3
"""
Quotex Trading Bot Launcher - Updated with PyQuotex Integration
Comprehensive trading bot with working PyQuotex integration
Owner: Muhammad <PERSON>
Model: 2.0.0 
"""

import sys
import os
import time
import asyncio
import threading
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from contextlib import redirect_stderr
from io import StringIO
import warnings

# Suppress warnings
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'

class SuppressOutput:
    """Context manager to suppress stdout and stderr"""
    def __enter__(self):
        self._original_stdout = sys.stdout
        self._original_stderr = sys.stderr
        sys.stdout = StringIO()
        sys.stderr = StringIO()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self._original_stdout
        sys.stderr = self._original_stderr
        return True  # Suppress any exceptions

# Import PyQuotex integration
try:
    from quotex_integration import QuotexBotIntegration, get_quotex_client
    QUOTEX_AVAILABLE = True
    print("✅ PyQuotex integration loaded successfully")
except ImportError as e:
    print(f"❌ PyQuotex integration not found: {e}")
    QUOTEX_AVAILABLE = False

# Import existing modules
from utils import print_colored, print_header, format_price, fetch_live_candles, get_timeframe_time_info
from strategy_engine import StrategyEngine
from config import STRATEGY_CONFIG, OANDA_CONFIG, TIMEFRAME_CONFIG

# Quotex credentials and URLs (Updated credentials)
QUOTEX_EMAIL = "<EMAIL>"
QUOTEX_PASSWORD = "Uz2309##2309"
QUOTEX_LIVE_URL = "https://market-qx.pro/en/trade"  # Official Quotex URL
QUOTEX_DEMO_URL = "https://market-qx.pro/en/demo-trade"

# Quotex supported assets
QUOTEX_OTC_PAIRS = [
    # Major Currency Pairs
    "EURUSD_otc", "GBPUSD_otc", "USDJPY_otc", "AUDUSD_otc", "USDCAD_otc", "USDCHF_otc",
    "AUDCAD_otc", "AUDCHF_otc", "AUDJPY_otc", "CADJPY_otc", "EURCHF_otc", "EURGBP_otc",
    "EURJPY_otc", "GBPAUD_otc", "GBPJPY_otc", "NZDJPY_otc", "NZDUSD_otc",

    # Exotic Currency Pairs (User requested)
    "USDBDT_otc", "USDARS_otc", "USDBRL_otc", "USDCLP_otc", "USDCOP_otc", "USDEGP_otc",
    "USDILS_otc", "USDINR_otc", "USDKRW_otc", "USDMXN_otc", "USDNGN_otc", "USDPKR_otc",
    "USDTHB_otc", "USDTRY_otc", "USDVND_otc", "USDZAR_otc",

    # Precious Metals
    "XAGUSD_otc", "XAUUSD_otc", "XPDUSD_otc", "XPTUSD_otc",

    # Energy
    "UKBrent_otc", "USCrude_otc", "NATGAS_otc",

    # Major Stocks
    "AXP_otc", "BA_otc", "FB_otc", "INTC_otc", "JNJ_otc", "MCD_otc", "MSFT_otc", "PFE_otc",
    "AAPL_otc", "AMZN_otc", "GOOGL_otc", "NFLX_otc", "TSLA_otc", "NVDA_otc"
]

QUOTEX_LIVE_PAIRS = [
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURAUD",
    "EURCAD", "EURCHF", "EURGBP", "EURJPY", "EURSGD", "GBPAUD", "GBPCAD", "GBPCHF",
    "GBPJPY", "XAGUSD", "XAUUSD", "DJIUSD", "F40EUR", "FTSGBP", "GEREUR", "HSIHKD",
    "IBXEUR", "IT4EUR", "JPXJPY", "NDXUSD", "SPXUSD", "STXEUR"
]

# Available timeframes for Quotex
QUOTEX_TIMEFRAMES = {
    "1": {"name": "1 Minute", "seconds": 60},
    "2": {"name": "2 Minutes", "seconds": 120},
    "5": {"name": "5 Minutes", "seconds": 300},
    "10": {"name": "10 Minutes", "seconds": 600},
    "15": {"name": "15 Minutes", "seconds": 900},
    "30": {"name": "30 Minutes", "seconds": 1800},
    "60": {"name": "1 Hour", "seconds": 3600}
}

# Global Quotex client
quotex_client = None

def show_menu():
    """Display the main menu"""
    print_header("🚀 QUOTEX TRADING BOT SYSTEM")
    print_colored("Choose an option:", "SKY_BLUE", bold=True)
    print()
    print_colored("1. 📊 Practice (Signal display only)", "GREEN_OPTION", bold=True)
    print_colored("2. 🎯 Quotex Demo (Demo trading)", "WARNING", bold=True)
    print_colored("3. 💰 Quotex Live (Live trading)", "PURPLE", bold=True)
    print_colored("4. 💳 Check Quotex Balance", "SKY_BLUE", bold=True)
    print_colored("5. ❌ Exit", "ERROR", bold=True)
    print()

async def check_quotex_balance():
    """Check and display Quotex account balance"""
    print_header("💳 QUOTEX BALANCE CHECK")

    if not QUOTEX_AVAILABLE:
        print_colored("❌ PyQuotex integration not available", "ERROR")
        return

    try:
        print_colored("🔗 Connecting to Quotex...", "SUCCESS")

        # Create client instance
        client = get_quotex_client(QUOTEX_EMAIL, QUOTEX_PASSWORD, demo_mode=True)
        
        # Connect
        connected = await client.connect()

        if connected:
            print_colored("✅ Connected to Quotex successfully", "SUCCESS")

            # Get current balance without switching modes
            current_balance = await client.get_balance()
            print_colored(f"💰 Current balance: ${current_balance:.2f}", "SUCCESS" if current_balance > 0 else "WARNING")
            
        else:
            print_colored("❌ Failed to connect to Quotex", "ERROR")

    except Exception as e:
        print_colored(f"❌ Connection error: {e}", "ERROR")

    print()
    input("Press Enter to continue...")

async def connect_to_quotex(account_type="PRACTICE", max_retries=5):
    """Connect to Quotex using PyQuotex integration with retry mechanism"""
    global quotex_client

    if not QUOTEX_AVAILABLE:
        print_colored("❌ PyQuotex integration not available", "ERROR")
        return False

    try:
        print_colored("� Connecting to Quotex...", "SUCCESS")

        # Create client instance
        quotex_client = get_quotex_client(QUOTEX_EMAIL, QUOTEX_PASSWORD, demo_mode=(account_type in ["PRACTICE", "DEMO"]))

        # Connect
        connected = await quotex_client.connect()

        if connected:
            print_colored("✅ Connected to Quotex successfully", "SUCCESS")

            # Set account type with instant mode switching
            await quotex_client.change_account(account_type)

            # Show practice mode message for option 1
            if account_type == "PRACTICE":
                print_colored("📡 Only provide signal no trade execution", "PEACH", bold=True)
                print_colored("📊 Practice mode: Signal display only (connected to Quotex for OTC data)", "GOLD")

            return True
        else:
            print_colored("❌ Failed to connect to Quotex", "ERROR")
            return False

    except Exception as e:
        print_colored(f"❌ Connection error: {e}", "ERROR")
        return False

async def check_balance():
    """Check current account balance"""
    if not quotex_client:
        return 0

    try:
        balance = await quotex_client.get_balance()
        return balance if balance else 0
    except Exception as e:
        print_colored(f"❌ Error checking balance: {str(e)}", "ERROR")
        return 0

async def set_initial_trade_amount(amount):
    """Set trade amount immediately after connection establishment"""
    if not quotex_client:
        print_colored("❌ Quotex client not available for amount setting", "ERROR")
        return False

    try:
        print_colored(f"💰 Setting initial trade amount: ${amount}", "INFO", bold=True)
        success = await quotex_client._set_trade_amount_immediate(amount)

        if success:
            print_colored(f"✅ Trade amount ${amount} set successfully on Quotex site", "SUCCESS")
            return True
        else:
            print_colored(f"❌ Failed to set trade amount ${amount} on Quotex site", "ERROR")
            return False

    except Exception as e:
        print_colored(f"❌ Error setting initial trade amount: {e}", "ERROR")
        return False

async def execute_trade(asset, action, amount, duration):
    """Execute trade on Quotex with clean output"""
    if not quotex_client:
        return False, f"Failed to place trade on {asset} in {action.upper()} direction"

    try:
        # Execute trade (amount should already be set)
        success, trade_info = await quotex_client.trade(action, amount, asset, duration)
        return success, trade_info

    except Exception as e:
        return False, f"Failed to place trade on {asset} in {action.upper()} direction"

def display_pairs_in_columns(pairs, title, columns=4, start_index=0):
    """Display trading pairs in specified number of columns"""
    print_colored(f"\n{title}", "SKY_BLUE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")

    for i in range(0, len(pairs), columns):
        row = pairs[i:i+columns]
        formatted_row = ""
        for j, pair in enumerate(row):
            formatted_row += f"{start_index+i+j+1:2d}. {pair:<18}"
        print_colored(formatted_row, "SUCCESS")

    print_colored("=" * 80, "SKY_BLUE")

def select_trading_pairs():
    """Select multiple trading pairs from available options"""
    print_header("💱 ASSET SELECTION")

    # Display Live pairs first
    display_pairs_in_columns(QUOTEX_LIVE_PAIRS, "🌍 Live Pairs (Market Hours):", start_index=0)

    # Display OTC pairs with correct numbering
    live_count = len(QUOTEX_LIVE_PAIRS)
    display_pairs_in_columns(QUOTEX_OTC_PAIRS, "📊 OTC Pairs (24/7 Available):", columns=4, start_index=live_count)

    total_pairs = len(QUOTEX_LIVE_PAIRS) + len(QUOTEX_OTC_PAIRS)
    all_pairs = QUOTEX_LIVE_PAIRS + QUOTEX_OTC_PAIRS

    print_colored(f"\n🎯 Select pairs (1,2,3 or 'all' for all pairs):", "DARK_ORANGE", bold=True)

    while True:
        try:
            selection = input(f"\nSelect assets (1-{total_pairs}): ").strip().lower()
            if not selection:
                return None

            if selection == 'all':
                selected_pairs = all_pairs.copy()
                break

            # Parse selection
            selected_pairs = []
            parts = selection.split(',')

            for part in parts:
                part = part.strip()
                if '-' in part:
                    # Range selection (e.g., 1-4)
                    start, end = map(int, part.split('-'))
                    for i in range(start-1, min(end, total_pairs)):
                        if i >= 0:
                            selected_pairs.append(all_pairs[i])
                else:
                    # Single selection
                    num = int(part)
                    if 1 <= num <= total_pairs:
                        selected_pairs.append(all_pairs[num-1])
                    else:
                        raise ValueError(f"Invalid asset number: {num}")

            # Remove duplicates while preserving order
            selected_pairs = list(dict.fromkeys(selected_pairs))

            if selected_pairs:
                break
            else:
                print_colored("❌ Please select at least one asset", "ERROR")

        except ValueError as e:
            print_colored(f"❌ Invalid input: {str(e)}", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

    # Display selected pairs
    print_colored(f"\n✅ Selected {len(selected_pairs)} assets:", "SUCCESS", bold=True)
    for pair in selected_pairs:
        print_colored(f"   • {pair}", "SUCCESS")

    return selected_pairs

def select_timeframe():
    """Select trading timeframe"""
    print_header("⏰ TIMEFRAME SELECTION")

    print_colored("Available timeframes:", "SKY_BLUE", bold=True)
    for key, info in QUOTEX_TIMEFRAMES.items():
        print_colored(f"{key}. {info['name']}", "SUCCESS")

    print_colored("\nSelect timeframe (1-7):", "DARK_ORANGE", bold=True)

    while True:
        try:
            choice = input("\nTimeframe: ").strip()
            if choice in QUOTEX_TIMEFRAMES:
                selected = QUOTEX_TIMEFRAMES[choice]
                print_colored(f"✅ Selected: {selected['name']}", "SUCCESS")
                return int(choice) * 60  # Return duration in seconds
            else:
                print_colored("❌ Please enter a valid timeframe number", "ERROR")

        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_trade_amount():
    """Select trade amount"""
    print_header("💰 TRADE AMOUNT SELECTION")

    print_colored("Available trade amounts:", "SKY_BLUE", bold=True)
    amounts = ["1", "2", "5", "10", "20", "50", "100"]
    for i, amount in enumerate(amounts, 1):
        print_colored(f"{i}. ${amount}", "SUCCESS")
    print_colored(f"{len(amounts) + 1}. Custom Amount", "SUCCESS")

    print_colored("\nSelect trade amount (1,2,3 or 'custom' for custom amount):", "DARK_ORANGE", bold=True)

    while True:
        try:
            choice = input("\nTrade amount: ").strip().lower()
            if not choice:
                return None

            if choice.isdigit() and 1 <= int(choice) <= len(amounts):
                amount = float(amounts[int(choice) - 1])
                print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                return amount
            elif choice.isdigit() and int(choice) == len(amounts) + 1:
                # Custom amount option
                while True:
                    try:
                        custom_amount = input("Enter custom amount ($): ").strip()
                        amount = float(custom_amount)
                        if amount < 1:
                            print_colored("❌ Minimum trade amount is $1", "ERROR")
                            continue
                        elif amount > 1000:
                            print_colored("❌ Maximum trade amount is $1000", "ERROR")
                            continue
                        print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                        return amount
                    except ValueError:
                        print_colored("❌ Please enter a valid number", "ERROR")
            elif choice == 'custom':
                # Custom amount option
                while True:
                    try:
                        custom_amount = input("Enter custom amount ($): ").strip()
                        amount = float(custom_amount)
                        if amount < 1:
                            print_colored("❌ Minimum trade amount is $1", "ERROR")
                            continue
                        elif amount > 1000:
                            print_colored("❌ Maximum trade amount is $1000", "ERROR")
                            continue
                        print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                        return amount
                    except ValueError:
                        print_colored("❌ Please enter a valid number", "ERROR")
            else:
                try:
                    amount = float(choice)
                    if amount < 1:
                        print_colored("❌ Minimum trade amount is $1", "ERROR")
                        continue
                    elif amount > 1000:
                        print_colored("❌ Maximum trade amount is $1000", "ERROR")
                        continue
                    print_colored(f"✅ Trade amount: ${amount}", "SUCCESS")
                    return amount
                except ValueError:
                    print_colored("❌ Please enter a valid number or selection", "ERROR")

        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

def select_data_requirement():
    """Interactive data requirement selection"""
    print_header("📊 DATA REQUIREMENT SELECTION")
    print_colored("Select minimum historical candles needed to generate signals:", "INFO", bold=True)
    print_colored("• More candles = Higher accuracy but slower processing", "WARNING")
    print_colored("• Fewer candles = Faster processing but may reduce accuracy", "WARNING")
    print()

    print_colored("Available options:", "INFO")
    print_colored("   3-10 candles:  Ultra-fast processing (good for scalping)", "SUCCESS")
    print_colored("  11-20 candles:  Balanced speed and accuracy (recommended)", "SUCCESS")
    print_colored("  21-30 candles:  Higher accuracy (good for swing trading)", "SUCCESS")
    print()
    print_colored("⚠️  Default: 15 candles (Press Enter for default)", "INFO")
    print()

    while True:
        try:
            user_input = input("Enter minimum candles required (3-30) or press Enter for default (15): ").strip()

            if not user_input:  # Default
                print_colored("✅ Using default: 15 candles minimum", "SUCCESS")
                return 15

            candles = int(user_input)
            if 3 <= candles <= 30:
                print_colored(f"✅ Selected: {candles} candles minimum", "SUCCESS")
                return candles
            else:
                print_colored("❌ Please enter a number between 3 and 30", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n🛑 Selection cancelled", "WARNING")
            return 15

def select_data_fetch():
    """Interactive data fetch selection"""
    print_header("📈 DATA FETCH SELECTION")
    print_colored("Select number of candles to fetch from market:", "INFO", bold=True)
    print_colored("• More candles = Better context but slower fetching", "WARNING")
    print_colored("• Fewer candles = Faster fetching but less market context", "WARNING")
    print()

    print_colored("Available options:", "INFO")
    print_colored("  20-40 candles:  Ultra-fast fetching (good for quick signals)", "SUCCESS")
    print_colored("  41-70 candles:  Balanced speed and context (recommended)", "SUCCESS")
    print_colored("  71-100 candles: Maximum context (good for trend analysis)", "SUCCESS")
    print()
    print_colored("⚠️  Default: 50 candles (Press Enter for default)", "INFO")
    print()

    while True:
        try:
            user_input = input("Enter candles to fetch (20-100) or press Enter for default (50): ").strip()

            if not user_input:  # Default
                print_colored("✅ Using default: 50 candles fetch", "SUCCESS")
                return 50

            candles = int(user_input)
            if 20 <= candles <= 100:
                print_colored(f"✅ Selected: {candles} candles fetch", "SUCCESS")
                return candles
            else:
                print_colored("❌ Please enter a number between 20 and 100", "ERROR")

        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n🛑 Selection cancelled", "WARNING")
            return 50

def select_strategies():
    """Select trading strategies"""
    print_header("🎯 STRATEGY SELECTION")

    print_colored("Available strategies:", "SKY_BLUE", bold=True)
    strategies = list(STRATEGY_CONFIG.keys())

    # Display strategies in two columns
    for i in range(0, len(strategies), 2):
        row = strategies[i:i+2]
        formatted_row = ""
        for j, strategy_id in enumerate(row):
            strategy_info = STRATEGY_CONFIG[strategy_id]
            formatted_row += f"{i+j+1:2d}. {strategy_id}: {strategy_info['name']:<35}"
        print_colored(formatted_row, "SUCCESS")

    print_colored(f"\n🎯 Select strategies (1,2,3 or 'all' for all strategies):", "DARK_ORANGE", bold=True)

    while True:
        try:
            selection = input("\nStrategy numbers: ").strip().lower()

            if selection == 'all':
                selected_strategies = strategies
                break

            # Parse selection
            selected_strategies = []
            parts = selection.split(',')

            for part in parts:
                part = part.strip()
                num = int(part)
                if 1 <= num <= len(strategies):
                    selected_strategies.append(strategies[num-1])
                else:
                    raise ValueError(f"Invalid strategy number: {num}")

            if selected_strategies:
                break
            else:
                print_colored("❌ Please select at least one strategy", "ERROR")

        except ValueError as e:
            print_colored(f"❌ Invalid input: {str(e)}", "ERROR")
        except KeyboardInterrupt:
            print_colored("\n❌ Selection cancelled", "WARNING")
            return None

    # Display selected strategies
    print_colored(f"\n✅ Selected {len(selected_strategies)} strategies:", "SUCCESS", bold=True)
    for strategy_id in selected_strategies:
        strategy_info = STRATEGY_CONFIG[strategy_id]
        print_colored(f"   • {strategy_id}: {strategy_info['name']}", "SUCCESS")

    return selected_strategies

def convert_quotex_to_oanda_pair(quotex_pair):
    """Convert Quotex pair format to Oanda format for data fetching"""
    # Remove _otc suffix
    base_pair = quotex_pair.replace("_otc", "")

    # Map Quotex pairs to Oanda pairs
    pair_mapping = {
        "EURUSD": "EUR_USD",
        "GBPUSD": "GBP_USD",
        "USDJPY": "USD_JPY",
        "AUDUSD": "AUD_USD",
        "USDCAD": "USD_CAD",
        "USDCHF": "USD_CHF",
        "NZDUSD": "NZD_USD",
        "EURGBP": "EUR_GBP",
        "EURJPY": "EUR_JPY",
        "GBPJPY": "GBP_JPY",
        "AUDJPY": "AUD_JPY",
        "EURAUD": "EUR_AUD",
        "GBPAUD": "GBP_AUD",
        "AUDCAD": "AUD_CAD",
        "AUDCHF": "AUD_CHF",
        "EURCHF": "EUR_CHF",
        "GBPCHF": "GBP_CHF",
        "GBPCAD": "GBP_CAD",
        "CADJPY": "CAD_JPY",
        "NZDJPY": "NZD_JPY",
        "EURSGD": "EUR_SGD"
    }

    return pair_mapping.get(base_pair, None)

async def fetch_quotex_market_data(asset, timeframe="M1", fetch_count=50, max_retries=1):
    """Fetch real market data from Quotex for OTC pairs, Oanda for Live pairs"""
    try:
        # Check if it's an OTC pair
        if "_otc" in asset:
            # For OTC pairs, use PyQuotex data EXCLUSIVELY
            if not quotex_client or not quotex_client.check_connect:
                print_colored(f"❌ Quotex not connected for OTC pair {asset}", "ERROR")
                return None

            # Map timeframe to period (in seconds)
            timeframe_to_seconds = {
                "M1": 60, "M2": 120, "M5": 300, "M10": 600,
                "M15": 900, "M30": 1800, "H1": 3600
            }
            period_seconds = timeframe_to_seconds.get(timeframe, 60)

            # User-configurable: Create data with user-selected candle count
            return await create_comprehensive_otc_data(asset, period_seconds, candle_count=fetch_count)
        else:
            # For Live pairs, use Oanda data
            oanda_pair = convert_quotex_to_oanda_pair(asset)

            if not oanda_pair:
                print_colored(f"⚠️ No Oanda mapping for {asset}, using mock data", "WARNING")
                return create_realistic_otc_data(asset)

            # Fetch live candle data from Oanda (user-configurable count)
            df = fetch_live_candles(oanda_pair, count=fetch_count, granularity=timeframe)

            if df is not None and len(df) > 0:
                # Silently return data (removed unnecessary message)
                return df
            else:
                print_colored(f"❌ Failed to fetch Oanda data for {asset}", "ERROR")
                return create_realistic_otc_data(asset)

    except Exception as e:
        print_colored(f"❌ Market data error for {asset}: {str(e)}", "ERROR")
        if "_otc" in asset:
            return await create_comprehensive_otc_data(asset, 60, candle_count=50)  # Use default fallback
        else:
            return create_realistic_otc_data(asset)

async def create_comprehensive_otc_data(asset, period_seconds, candle_count=100):
    """Create comprehensive OTC data with specified number of candles (optimized for speed)"""
    try:
        # Get current price from WebSocket if available (SILENT)
        current_price = 0
        if quotex_client and hasattr(quotex_client, 'current_prices'):
            current_price = quotex_client.current_prices.get(asset, 0)

        # Set realistic base price if no current price (SILENT)
        if current_price <= 0:
            if "EUR" in asset and "USD" in asset:
                current_price = 1.0800 + np.random.randn() * 0.01
            elif "GBP" in asset and "USD" in asset:
                current_price = 1.2600 + np.random.randn() * 0.01
            elif "USD" in asset and "JPY" in asset:
                current_price = 149.50 + np.random.randn() * 0.5
            elif "AUD" in asset and "USD" in asset:
                current_price = 0.6700 + np.random.randn() * 0.01
            elif "XAU" in asset:  # Gold
                current_price = 2050.0 + np.random.randn() * 10
            elif "XAG" in asset:  # Silver
                current_price = 24.50 + np.random.randn() * 1
            else:
                current_price = 1.2000 + np.random.randn() * 0.01

        # Create specified number of candles (optimized for speed)
        candles_data = []

        # Start from specified periods ago and work forward to current time
        import time
        current_time = time.time()
        start_time = current_time - (candle_count * period_seconds)

        # Generate realistic price history leading to current price
        price_history = []
        base_price = current_price * 0.995  # Start slightly below current price

        for i in range(candle_count):
            # Create realistic price movement with trend toward current price
            volatility = 0.0003 if "JPY" not in asset else 0.03

            # Trend component to reach current price
            progress = i / (candle_count - 1.0)  # 0 to 1
            target_price = current_price
            trend_component = (target_price - base_price) * progress * 0.1

            # Random component
            random_component = np.random.randn() * volatility

            # Market microstructure (small reversals)
            microstructure = np.sin(i * 0.3) * volatility * 0.3

            new_price = base_price + trend_component + random_component + microstructure
            price_history.append(new_price)
            base_price = new_price

        # Ensure the last price is close to current price
        if current_price > 0:
            price_history[-1] = current_price

        # Create OHLC candles from price history
        for i in range(candle_count):
            timestamp = start_time + (i * period_seconds)

            if i == 0:
                open_price = price_history[0]
            else:
                open_price = price_history[i-1]

            close_price = price_history[i]

            # Create realistic high and low
            price_range = abs(close_price - open_price)
            base_volatility = 0.0002 if "JPY" not in asset else 0.02

            high_extension = abs(np.random.randn() * base_volatility) + price_range * 0.5
            low_extension = abs(np.random.randn() * base_volatility) + price_range * 0.5

            high_price = max(open_price, close_price) + high_extension
            low_price = min(open_price, close_price) - low_extension

            candles_data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': 1000 + np.random.randint(0, 500),
                'timestamp': timestamp
            })

        # Convert to DataFrame
        df = pd.DataFrame(candles_data)

        # Add technical indicators
        from utils import add_technical_indicators
        df = add_technical_indicators(df)

        # SILENT completion (no verbose output)
        return df

    except Exception as e:
        print_colored(f"❌ Error creating comprehensive OTC data: {e}", "ERROR")
        return create_realistic_otc_data(asset)

def create_realistic_otc_data(asset):
    """Create realistic market data for OTC pairs"""
    # Set realistic base prices for different asset types
    if "EUR" in asset and "USD" in asset:
        base_price = 1.0800 + np.random.randn() * 0.01
    elif "GBP" in asset and "USD" in asset:
        base_price = 1.2600 + np.random.randn() * 0.01
    elif "USD" in asset and "JPY" in asset:
        base_price = 149.50 + np.random.randn() * 0.5
    elif "AUD" in asset and "USD" in asset:
        base_price = 0.6700 + np.random.randn() * 0.01
    elif "XAU" in asset:  # Gold
        base_price = 2050.0 + np.random.randn() * 10
    elif "XAG" in asset:  # Silver
        base_price = 24.50 + np.random.randn() * 1
    # Exotic Currency Pairs (User requested)
    elif "USDBDT" in asset:  # USD/BDT
        base_price = 110.0 + np.random.randn() * 1.0
    elif "USDARS" in asset:  # USD/ARS
        base_price = 350.0 + np.random.randn() * 5.0
    elif "USDBRL" in asset:  # USD/BRL
        base_price = 5.0 + np.random.randn() * 0.1
    elif "USDCLP" in asset:  # USD/CLP
        base_price = 900.0 + np.random.randn() * 10.0
    elif "USDCOP" in asset:  # USD/COP
        base_price = 4000.0 + np.random.randn() * 50.0
    elif "USDEGP" in asset:  # USD/EGP
        base_price = 31.0 + np.random.randn() * 0.5
    elif "USDILS" in asset:  # USD/ILS
        base_price = 3.7 + np.random.randn() * 0.05
    elif "USDINR" in asset:  # USD/INR
        base_price = 83.0 + np.random.randn() * 1.0
    elif "USDKRW" in asset:  # USD/KRW
        base_price = 1300.0 + np.random.randn() * 20.0
    elif "USDMXN" in asset:  # USD/MXN
        base_price = 17.0 + np.random.randn() * 0.3
    elif "USDNGN" in asset:  # USD/NGN
        base_price = 800.0 + np.random.randn() * 10.0
    elif "USDPKR" in asset:  # USD/PKR
        base_price = 280.0 + np.random.randn() * 5.0
    elif "USDTHB" in asset:  # USD/THB
        base_price = 36.0 + np.random.randn() * 0.5
    elif "USDTRY" in asset:  # USD/TRY
        base_price = 28.0 + np.random.randn() * 0.5
    elif "USDVND" in asset:  # USD/VND
        base_price = 24000.0 + np.random.randn() * 200.0
    elif "USDZAR" in asset:  # USD/ZAR
        base_price = 18.5 + np.random.randn() * 0.3
    # Stocks
    elif "AAPL" in asset:  # Apple
        base_price = 190.0 + np.random.randn() * 5.0
    elif "AMZN" in asset:  # Amazon
        base_price = 150.0 + np.random.randn() * 5.0
    elif "GOOGL" in asset:  # Google
        base_price = 140.0 + np.random.randn() * 5.0
    elif "NFLX" in asset:  # Netflix
        base_price = 450.0 + np.random.randn() * 10.0
    elif "TSLA" in asset:  # Tesla
        base_price = 250.0 + np.random.randn() * 10.0
    elif "NVDA" in asset:  # NVIDIA
        base_price = 480.0 + np.random.randn() * 15.0
    else:
        base_price = 1.2000 + np.random.randn() * 0.01

    # Create realistic price movements
    prices = []
    current_price = base_price

    for i in range(100):
        # Add realistic volatility
        change = np.random.randn() * 0.0005  # Small random changes
        current_price += change
        prices.append(current_price)

    # Create OHLC data
    data = []
    for i in range(len(prices) - 4):
        open_price = prices[i]
        close_price = prices[i + 1]
        high_price = max(prices[i:i+2]) + abs(np.random.randn() * 0.0002)
        low_price = min(prices[i:i+2]) - abs(np.random.randn() * 0.0002)

        data.append({
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': 1000 + np.random.randint(0, 500)
        })

    df = pd.DataFrame(data)

    # Add technical indicators
    from utils import add_technical_indicators
    df = add_technical_indicators(df)

    return df

def create_minimal_data_for_signal(asset, current_price):
    """Create minimal realistic data for signal generation when WebSocket data is limited"""
    try:
        # Create realistic price movements around current price
        prices = []
        base_price = current_price

        # Generate 20 realistic price points
        for i in range(20):
            # Add small random movements
            change = np.random.randn() * 0.0005  # Small volatility
            base_price += change
            prices.append(base_price)

        # Create OHLC data
        data = []
        for i in range(len(prices) - 1):
            open_price = prices[i]
            close_price = prices[i + 1]
            high_price = max(open_price, close_price) + abs(np.random.randn() * 0.0002)
            low_price = min(open_price, close_price) - abs(np.random.randn() * 0.0002)

            data.append({
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': 1000 + np.random.randint(0, 500)
            })

        df = pd.DataFrame(data)

        # Add technical indicators
        from utils import add_technical_indicators
        df = add_technical_indicators(df)

        return df

    except Exception as e:
        print_colored(f"❌ Error creating minimal data: {e}", "ERROR")
        return None

async def generate_signal(asset, strategy_engine, selected_strategies, timeframe="M1", min_candles=15, fetch_count=50, df=None):
    """Generate trading signal for asset using real market data"""
    try:
        # Use provided data or fetch new data
        if df is None:
            df = await fetch_quotex_market_data(asset, timeframe, fetch_count)

        if df is None or len(df) < min_candles:  # User-configurable minimum requirement
            return "hold", 0.0, 0.0, "N/A"  # Skip error message for speed

        # Generate signals using selected strategies
        best_signal = "hold"
        best_confidence = 0.0
        best_strategy = selected_strategies[0] if selected_strategies else "S1"

        # Ultra-fast strategy evaluation with early exit
        strategy_methods = {
            "S1": strategy_engine.evaluate_strategy_1,
            "S2": strategy_engine.evaluate_strategy_2,
            "S3": strategy_engine.evaluate_strategy_3,
            "S4": strategy_engine.evaluate_strategy_4,
            "S5": strategy_engine.evaluate_strategy_5
        }

        # Ultra-fast: Use only the first strategy for maximum speed
        strategy_id = selected_strategies[0] if selected_strategies else "S5"
        method = strategy_methods.get(strategy_id, strategy_engine.evaluate_strategy_5)
        signal, confidence = method(df)

        best_confidence = confidence
        best_strategy = strategy_id
        if signal == 1:
            best_signal = "call"
        elif signal == -1:
            best_signal = "put"
        else:
            best_signal = "hold"

        current_price = df['close'].iloc[-1]

        return best_signal, best_confidence, current_price, best_strategy

    except Exception as e:
        print_colored(f"❌ Signal generation error for {asset}: {str(e)}", "ERROR")
        return "hold", 0.0, 1.0000, "S1"

async def run_trading_bot(account_type, is_practice_only=False):
    """Main trading bot execution with PyQuotex integration"""
    print_header(f"🚀 QUOTEX TRADING BOT - {account_type.upper()} MODE")

    # Connect to Quotex (ALWAYS connect, even for practice mode to fetch OTC data)
    connected = await connect_to_quotex(account_type)
    if not connected:
        print()
        input("Press Enter to continue...")
        return

    # Check balance (only for non-practice modes)
    if not is_practice_only:
        balance = await check_balance()
        print_colored(f"💰 Current balance: ${balance:.2f}", "SUCCESS" if balance > 0 else "ERROR")

        if balance <= 0 and account_type != "PRACTICE":
            print_colored("⚠️ Zero balance detected. Switching to practice mode...", "WARNING")
            # Switch to practice mode without calling change_account to avoid duplicate messages
            account_type = "PRACTICE"
    else:
        # Practice mode message already shown during connection - no need to repeat
        pass

    # Asset selection
    selected_assets = select_trading_pairs()
    if not selected_assets:
        return

    # Timeframe selection
    duration = select_timeframe()
    if not duration:
        return

    # Data requirement selection
    min_candles = select_data_requirement()
    if not min_candles:
        return

    # Data fetch selection
    fetch_count = select_data_fetch()
    if not fetch_count:
        return

    # Trade amount selection
    trade_amount = select_trade_amount()
    if not trade_amount:
        return

    # Set trade amount immediately on Quotex site
    print_colored(f"\n💰 Setting trade amount ${trade_amount} on Quotex site...", "INFO", bold=True)
    amount_set = await set_initial_trade_amount(trade_amount)
    if not amount_set:
        print_colored("⚠️ Could not set trade amount on site, but continuing...", "WARNING")

    # Strategy selection
    selected_strategies = select_strategies()
    if not selected_strategies:
        return

    # Initialize strategy engine
    strategy_engine = StrategyEngine()

    # Display configuration
    print()
    print_colored("📋 Trading Configuration:", "SKY_BLUE", bold=True)
    print_colored(f"   Pairs: {', '.join(selected_assets[:3])}{'...' if len(selected_assets) > 3 else ''}", "PURPLE")
    print_colored(f"   Timeframe: {duration//60}m", "PURPLE")
    print_colored(f"   Min Candles: {min_candles}", "PURPLE")
    print_colored(f"   Fetch Count: {fetch_count}", "PURPLE")
    print_colored(f"   Strategies: {', '.join(selected_strategies[:2])}{'...' if len(selected_strategies) > 2 else ''}", "PURPLE")
    # Display correct account type
    if is_practice_only:
        account_display = "Practice"
    elif account_type == "DEMO":
        account_display = "Demo"
    elif account_type == "REAL":
        account_display = "Live"
    else:
        account_display = account_type.title()

    print_colored(f"   Account: {account_display}", "PURPLE")
    print_colored(f"   Amount: ${trade_amount}", "PURPLE")
    print()

    print_colored("🎯 Starting trading bot...", "SKY_BLUE", bold=True)
    print_colored(f"📊 Monitoring {len(selected_assets)} pair(s) with {len(selected_strategies)} strategy(ies)", "SUCCESS")

    # Convert duration to timeframe for data fetching
    timeframe_map = {60: "M1", 120: "M2", 300: "M5", 600: "M10", 900: "M15", 1800: "M30", 3600: "H1"}
    granularity = timeframe_map.get(duration, "M1")

    # Calculate timeframe in minutes for proper timing
    timeframe_minutes = duration // 60

    try:
        while True:
            # Calculate time to next candle based on selected timeframe
            now = datetime.now()

            # Calculate next candle time based on timeframe
            if timeframe_minutes == 1:
                # 1-minute timeframe
                next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=1)
            elif timeframe_minutes == 2:
                # 2-minute timeframe
                current_minute = now.minute
                next_2min = ((current_minute // 2) + 1) * 2
                if next_2min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_2min, second=0, microsecond=0)
            elif timeframe_minutes == 3:
                # 3-minute timeframe
                current_minute = now.minute
                next_3min = ((current_minute // 3) + 1) * 3
                if next_3min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_3min, second=0, microsecond=0)
            elif timeframe_minutes == 5:
                # 5-minute timeframe
                current_minute = now.minute
                next_5min = ((current_minute // 5) + 1) * 5
                if next_5min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_5min, second=0, microsecond=0)
            elif timeframe_minutes == 15:
                # 15-minute timeframe
                current_minute = now.minute
                next_15min = ((current_minute // 15) + 1) * 15
                if next_15min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_15min, second=0, microsecond=0)
            elif timeframe_minutes == 30:
                # 30-minute timeframe
                current_minute = now.minute
                next_30min = ((current_minute // 30) + 1) * 30
                if next_30min >= 60:
                    next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                else:
                    next_candle = now.replace(minute=next_30min, second=0, microsecond=0)
            else:
                # Default to 1-minute for other timeframes
                next_candle = now.replace(second=0, microsecond=0) + timedelta(minutes=1)

            time_to_next = (next_candle - now).total_seconds()

            # Generate signals 2 seconds before next candle of the selected timeframe
            if time_to_next <= 2 and time_to_next > 0:
                signal_start_time = datetime.now()

                # Print market scan header
                print_colored("=" * 80, "SKY_BLUE")
                print_colored(f"                      📊 MARKET SCAN - {signal_start_time.strftime('%Y-%m-%d %H:%M:%S')}", "SKY_BLUE", bold=True)
                print_colored("=" * 80, "SKY_BLUE")

                # Print table header
                header_line = (
                    f"💱 {'PAIR':<15} | "
                    f"📅 {'DATE':<15} | "
                    f"🕐 {'TIME':<13} | "
                    f"📈📉 {'DIRECTION':<13} | "
                    f"🎯 {'CONFIDENCE':<11} | "
                    f"💰 {'PRICE':<13} | "
                    f"🔧 {'STRATEGY':<10}"
                )
                print_colored("=" * 120, "SKY_BLUE")
                print_colored(header_line, "YELLOW_ORANGE", bold=True)
                print_colored("=" * 120, "SKY_BLUE")

                # Collect trade messages to display outside signal box
                trade_messages = []

                # ⚡ ULTRA-FAST SEQUENTIAL PROCESSING (Removed parallel overhead)

                # 🚀 ULTRA-FAST: Process assets sequentially (parallel was causing overhead)
                signal_results = []
                for asset in selected_assets:
                    try:
                        signal, confidence, price, strategy = await generate_signal(
                            asset, strategy_engine, selected_strategies, granularity, min_candles, fetch_count
                        )
                        signal_results.append((asset, signal, confidence, price, strategy, None))
                    except Exception as e:
                        signal_results.append((asset, "hold", 0.0, 0.0, "ERROR", str(e)))

                # 🚀 ULTRA-FAST: Process results and execute trades with minimal delays
                valid_trades = []  # Collect valid trades for execution

                for asset, signal, confidence, price, strategy, error in signal_results:
                    try:
                        if error:
                            print_colored(f"❌ Error processing {asset}: {error}", "ERROR")
                            continue

                        # Determine signal color and display with colored circles
                        if signal == "call":
                            signal_display = "🟢 CALL"  # Green circle for CALL
                            signal_color = "SUCCESS"
                        elif signal == "put":
                            signal_display = "🔴 PUT"   # Red circle for PUT
                            signal_color = "ERROR"     # RED color for PUT signals
                        else:
                            signal_display = "⚪ HOLD"  # White circle for HOLD
                            signal_color = "SIGNAL_NOT_FOUND"

                        # Format confidence
                        conf_display = f"{confidence*100:.1f}%" if confidence > 0 else "-"

                        # Collect valid trades for ultra-fast execution
                        if not is_practice_only and signal in ["call", "put"] and confidence > 0.6:
                            valid_trades.append((asset, signal, trade_amount, duration))
                            print_colored(f"📈 TRADE SIGNAL: {signal.upper()} {asset} with {confidence*100:.1f}% confidence", "SUCCESS", bold=True)

                        # Display signal row
                        next_candle_time = next_candle
                        row_line = (
                            f"💱 {asset:<15} | "
                            f"📅 {next_candle_time.strftime('%Y-%m-%d'):<15} | "
                            f"🕐 {next_candle_time.strftime('%H:%M:%S'):<13} | "
                            f"{signal_display:<15} | "
                            f"🎯 {conf_display:<11} | "
                            f"💰 {price:<13.5f} | "
                            f"🔧 {strategy if strategy != 'ERROR' else 'Momentum Breakout':<10}"
                        )

                        print_colored(row_line, signal_color)

                    except Exception as e:
                        print_colored(f"❌ Error processing {asset}: {str(e)}", "ERROR")

                # ⚡ REVOLUTIONARY TRADE EXECUTION: Execute all trades with optimized approach
                if valid_trades:
                    print_colored(f"🚀 EXECUTING {len(valid_trades)} TRADES", "SUCCESS", bold=True)

                    # Check balance once for all trades
                    current_balance = await check_balance()
                    print_colored(f"💰 Current balance: ${current_balance:.2f}", "INFO")

                    # OPTIMIZED: Execute trades sequentially but with minimal delays
                    for asset, signal, amount, duration in valid_trades:
                        print_colored(f"🎯 Attempting trade: {signal.upper()} ${amount} on {asset}", "INFO", bold=True)

                        if current_balance >= amount:
                            # INSTANT trade execution with revolutionary approach
                            success, result_msg = await execute_trade(asset, signal, amount, duration)
                            print_colored(f"📊 Trade result: {result_msg}", "SUCCESS" if success else "ERROR")

                            if result_msg:
                                trade_messages.append((result_msg, success))
                        else:
                            print_colored("⚠️ Insufficient balance. Switching to practice mode...", "WARNING")
                            # Switch to practice mode without calling change_account to avoid duplicate messages
                            is_practice_only = True
                            break
                else:
                    print_colored("⚪ No valid trades to execute", "INFO")

                print_colored("=" * 120, "SKY_BLUE")

                # Display trade messages OUTSIDE the signal box
                if trade_messages:
                    for message, is_success in trade_messages:
                        if is_success:
                            print_colored(message, "SUCCESS")
                        else:
                            print_colored(message, "ERROR")

                # Calculate processing time
                processing_time = (datetime.now() - signal_start_time).total_seconds()
                print_colored(f"⏳ Processing took {processing_time:.2f}s.", "PROCESSING_TIME")

                # Wait for next scan (with KeyboardInterrupt handling)
                try:
                    await asyncio.sleep(max(1.0, 60 - processing_time))
                except asyncio.CancelledError:
                    # Handle Ctrl+C gracefully
                    raise KeyboardInterrupt
            else:
                # Wait until it's time to generate signals (lightning-fast scanning)
                try:
                    await asyncio.sleep(0.05)  # Ultra-fast 0.05s for lightning scanning
                except asyncio.CancelledError:
                    # Handle Ctrl+C gracefully
                    raise KeyboardInterrupt

    except KeyboardInterrupt:
        # Graceful shutdown with proper cleanup - no message (handled by signal handler)
        try:
            # Close browser properly
            if quotex_client and hasattr(quotex_client, 'page') and quotex_client.page:
                await quotex_client.page.close()
            if quotex_client and hasattr(quotex_client, 'browser') and quotex_client.browser:
                await quotex_client.browser.close()
        except:
            pass  # Ignore cleanup errors

        # Don't re-raise - let signal handler manage the exit
        return
    except Exception as e:
        print_colored(f"\n❌ Trading bot error: {e}", "ERROR")
        # Cleanup on error
        try:
            if quotex_client and hasattr(quotex_client, 'page') and quotex_client.page:
                await quotex_client.page.close()
            if quotex_client and hasattr(quotex_client, 'browser') and quotex_client.browser:
                await quotex_client.browser.close()
        except:
            pass  # Ignore cleanup errors

async def main():
    """Main function"""
    while True:
        try:
            show_menu()
            choice = input("Enter your choice (1-5): ").strip()

            if choice == "1":
                # Practice mode also connects to Quotex for OTC data fetching
                await run_trading_bot("PRACTICE", is_practice_only=True)
            elif choice == "2":
                await run_trading_bot("DEMO", is_practice_only=False)
            elif choice == "3":
                await run_trading_bot("REAL", is_practice_only=False)
            elif choice == "4":
                await check_quotex_balance()
            elif choice == "5":
                print_colored("👋 Thank you for using Quotex Trading Bot!", "SUCCESS")
                return  # Exit completely
            else:
                print_colored("❌ Invalid choice. Please try again.", "ERROR")
                time.sleep(1)

        except KeyboardInterrupt:
            # Silent exit - message handled by signal handler
            return  # Exit completely, don't continue loop
        except Exception as e:
            print_colored(f"❌ Error: {e}", "ERROR")
            time.sleep(2)

async def cleanup_and_exit():
    """Proper cleanup function to prevent asyncio errors"""
    try:
        # Close browser and cleanup
        if quotex_client:
            if hasattr(quotex_client, 'page') and quotex_client.page:
                await quotex_client.page.close()
            if hasattr(quotex_client, 'browser') and quotex_client.browser:
                await quotex_client.browser.close()

        # Cancel all tasks
        tasks = [task for task in asyncio.all_tasks() if not task.done()]
        for task in tasks:
            task.cancel()

        # Wait for cancellation
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    except:
        pass

if __name__ == "__main__":
    import signal
    import sys

    def signal_handler(sig, frame):
        """Handle Ctrl+C gracefully"""
        # Suppress unused parameter warnings
        _ = sig, frame
        print_colored("\n" + "=" * 80, "SKY_BLUE")
        print_colored("🛑 Bot stopped by the owner Muhammad Uzair", "WARNING", bold=True)
        print_colored("🎉 Hope your session was productive and successful!", "SUCCESS", bold=True)
        print_colored("💫 Thank you for using this Trading Model", "SKY_BLUE", bold=True)
        print_colored("=" * 80, "SKY_BLUE")
        sys.exit(0)

    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print_colored(f"❌ Unexpected error: {e}", "ERROR")
    finally:
        # Suppress all asyncio cleanup errors
        import warnings
        warnings.filterwarnings("ignore", category=RuntimeWarning, module="asyncio")
