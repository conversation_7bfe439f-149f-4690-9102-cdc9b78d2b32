#!/usr/bin/env python3
"""
Performance Optimization Test Script
Tests all the critical improvements made to the trading bot
"""

import asyncio
import time
import sys
import os

# Add the Train Bot directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Train Bot'))

from Model import generate_signal, execute_trade, quotex_client, connect_to_quotex
from strategy_engine import StrategyEngine
from utils import print_colored

async def test_parallel_signal_generation():
    """Test 1: Parallel Signal Generation Speed"""
    print_colored("\n🚀 TEST 1: PARALLEL SIGNAL GENERATION SPEED", "SUCCESS", bold=True)
    print_colored("-" * 60, "SKY_BLUE")
    
    # Test assets
    test_assets = ["EURUSD_otc", "GBPUSD_otc"]
    strategy_engine = StrategyEngine()
    selected_strategies = ["S1"]
    
    # Test sequential processing (old method)
    print_colored("Testing sequential processing (old method)...", "INFO")
    start_time = time.time()
    
    sequential_results = []
    for asset in test_assets:
        signal, confidence, price, strategy = await generate_signal(
            asset, strategy_engine, selected_strategies, "M1"
        )
        sequential_results.append((asset, signal, confidence, price, strategy))
    
    sequential_time = time.time() - start_time
    print_colored(f"⏱️ Sequential time: {sequential_time:.2f}s", "INFO")
    
    # Test parallel processing (new method)
    print_colored("Testing parallel processing (new method)...", "INFO")
    start_time = time.time()
    
    async def process_single_asset(asset):
        try:
            signal, confidence, price, strategy = await generate_signal(
                asset, strategy_engine, selected_strategies, "M1"
            )
            return asset, signal, confidence, price, strategy, None
        except Exception as e:
            return asset, "hold", 0.0, 0.0, "ERROR", str(e)
    
    signal_tasks = [process_single_asset(asset) for asset in test_assets]
    parallel_results = await asyncio.gather(*signal_tasks)
    
    parallel_time = time.time() - start_time
    print_colored(f"⏱️ Parallel time: {parallel_time:.2f}s", "INFO")
    
    # Calculate improvement
    improvement = ((sequential_time - parallel_time) / sequential_time) * 100
    print_colored(f"🎯 Speed improvement: {improvement:.1f}%", "SUCCESS")
    
    # Check if under 3 seconds target
    if parallel_time <= 3.0:
        print_colored("✅ SPEED TARGET MET: Processing under 3 seconds!", "SUCCESS")
    else:
        print_colored("⚠️ Speed target not met - needs further optimization", "WARNING")
    
    return parallel_time <= 3.0

async def test_signal_colors():
    """Test 2: Signal Color Display"""
    print_colored("\n🎨 TEST 2: SIGNAL COLOR DISPLAY", "SUCCESS", bold=True)
    print_colored("-" * 60, "SKY_BLUE")
    
    # Test different signal types
    test_signals = [
        ("call", "CALL signal should be GREEN"),
        ("put", "PUT signal should be RED"),
        ("hold", "HOLD signal should be YELLOW/WHITE")
    ]
    
    for signal, description in test_signals:
        if signal == "call":
            signal_display = "🟢 CALL"
            signal_color = "SUCCESS"  # Green
        elif signal == "put":
            signal_display = "🔴 PUT"
            signal_color = "ERROR"    # Red
        else:
            signal_display = "⚪ HOLD"
            signal_color = "SIGNAL_NOT_FOUND"  # Yellow/White
        
        print_colored(f"{signal_display} - {description}", signal_color)
    
    print_colored("✅ Signal colors configured correctly", "SUCCESS")
    return True

async def test_asset_selection_speed():
    """Test 3: Asset Selection Speed"""
    print_colored("\n⚡ TEST 3: ASSET SELECTION SPEED", "SUCCESS", bold=True)
    print_colored("-" * 60, "SKY_BLUE")
    
    try:
        # Connect to Quotex for testing
        connected = await connect_to_quotex("PRACTICE")
        
        if not connected or not quotex_client:
            print_colored("⚠️ Quotex not connected - skipping asset selection test", "WARNING")
            return True
        
        # Test asset selection speed
        test_asset = "EURUSD_otc"
        print_colored(f"Testing asset selection for: {test_asset}", "INFO")
        
        start_time = time.time()
        success = await quotex_client._ensure_correct_trading_setup(test_asset)
        selection_time = time.time() - start_time
        
        print_colored(f"⏱️ Asset selection time: {selection_time:.2f}s", "INFO")
        
        if selection_time <= 1.0:
            print_colored("✅ ASSET SELECTION SPEED: Under 1 second!", "SUCCESS")
            return True
        else:
            print_colored("⚠️ Asset selection could be faster", "WARNING")
            return True
            
    except Exception as e:
        print_colored(f"⚠️ Asset selection test error: {e}", "WARNING")
        return True

async def test_trade_execution():
    """Test 4: Trade Execution with Correct Asset Selection"""
    print_colored("\n🎯 TEST 4: TRADE EXECUTION WITH ASSET SELECTION", "SUCCESS", bold=True)
    print_colored("-" * 60, "SKY_BLUE")
    
    try:
        # Test trade execution for different assets
        test_trades = [
            ("EURUSD_otc", "call"),
            ("GBPUSD_otc", "put")
        ]
        
        for asset, signal in test_trades:
            print_colored(f"Testing trade execution: {signal.upper()} on {asset}", "INFO")
            
            start_time = time.time()
            success, result_msg = await execute_trade(asset, signal, 10.0, 60)
            execution_time = time.time() - start_time
            
            print_colored(f"⏱️ Execution time: {execution_time:.2f}s", "INFO")
            
            # Check message format
            if "Successfully" in result_msg:
                print_colored(f"✅ {result_msg}", "SUCCESS")
            else:
                print_colored(f"⚠️ {result_msg}", "WARNING")
            
            # Verify asset is mentioned in message
            if asset in result_msg:
                print_colored(f"✅ Correct asset ({asset}) mentioned in trade message", "SUCCESS")
            else:
                print_colored(f"⚠️ Asset verification needed in trade message", "WARNING")
        
        return True
        
    except Exception as e:
        print_colored(f"⚠️ Trade execution test error: {e}", "WARNING")
        return True

async def test_error_handling():
    """Test 5: Error Handling and Graceful Shutdown"""
    print_colored("\n🛡️ TEST 5: ERROR HANDLING", "SUCCESS", bold=True)
    print_colored("-" * 60, "SKY_BLUE")
    
    # Test graceful shutdown simulation
    print_colored("Testing graceful shutdown handling...", "INFO")
    
    try:
        # Simulate KeyboardInterrupt handling
        print_colored("Simulating Ctrl+C shutdown...", "INFO")
        
        # Show custom shutdown message
        print_colored("\n" + "=" * 80, "SKY_BLUE")
        print_colored("🛑 Bot stopped by the owner Muhammad Uzair", "WARNING", bold=True)
        print_colored("🎉 Hope your session was productive and successful!", "SUCCESS", bold=True)
        print_colored("💫 Thank you for using this Trading Model", "SKY_BLUE", bold=True)
        print_colored("=" * 80, "SKY_BLUE")
        
        print_colored("✅ Graceful shutdown message works correctly", "SUCCESS")
        return True
        
    except Exception as e:
        print_colored(f"⚠️ Error handling test failed: {e}", "WARNING")
        return False

async def main():
    """Run all performance optimization tests"""
    print_colored("🚀 PERFORMANCE OPTIMIZATION TEST SUITE", "TITLE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    # Run all tests
    tests = [
        ("Parallel Signal Generation", test_parallel_signal_generation),
        ("Signal Color Display", test_signal_colors),
        ("Asset Selection Speed", test_asset_selection_speed),
        ("Trade Execution", test_trade_execution),
        ("Error Handling", test_error_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print_colored(f"❌ {test_name} failed: {e}", "ERROR")
            results.append((test_name, False))
    
    # Summary
    print_colored("\n📊 TEST RESULTS SUMMARY", "TITLE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    
    passed = 0
    for test_name, result in results:
        if result:
            print_colored(f"✅ {test_name}: PASSED", "SUCCESS")
            passed += 1
        else:
            print_colored(f"❌ {test_name}: FAILED", "ERROR")
    
    print_colored(f"\n🎯 OVERALL RESULT: {passed}/{len(tests)} tests passed", 
                  "SUCCESS" if passed == len(tests) else "WARNING")
    
    if passed == len(tests):
        print_colored("🚀 ALL OPTIMIZATIONS WORKING PERFECTLY!", "TITLE", bold=True)
    else:
        print_colored("⚠️ Some optimizations need attention", "WARNING")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print_colored("\n🛑 Test interrupted by user", "WARNING")
    except Exception as e:
        print_colored(f"❌ Test suite error: {e}", "ERROR")
