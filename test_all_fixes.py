#!/usr/bin/env python3
"""
Test all fixes: time issue, colors, and clean output
"""

import asyncio
import sys
import os

# Add the Train Bot directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Train Bot'))

from utils import print_colored

async def main():
    """Test all fixes"""
    print_colored("🎯 COMPREHENSIVE FIXES TEST", "TITLE", bold=True)
    print_colored("=" * 80, "SKY_BLUE")
    print_colored("Testing all implemented fixes:", "INFO")
    
    print_colored("\n✅ FIXES IMPLEMENTED:", "SUCCESS", bold=True)
    print_colored("1. Time (tempo) issue fixed - minimum 60 seconds duration", "SUCCESS")
    print_colored("2. Investment amount setting - clean output with required lines only", "SUCCESS")
    print_colored("3. Trade execution - silent execution, no verbose messages", "SUCCESS")
    print_colored("4. Connection messages - light gray color", "SUCCESS")
    print_colored("5. Signal headers - teal color", "SUCCESS")
    print_colored("6. Pairs/timeframe/strategy/amount - crimson color", "SUCCESS")
    print_colored("7. Trading configuration - royal purple color", "SUCCESS")
    print_colored("8. Main menu option 3 - burnt orange color", "SUCCESS")
    
    print_colored("\n🎯 EXPECTED BEHAVIOR:", "INFO", bold=True)
    print_colored("• Time issue fixed: trades execute successfully", "INFO")
    print_colored("• Clean investment setting: only 4 required lines", "INFO")
    print_colored("• Silent trade execution: no verbose trade messages", "INFO")
    print_colored("• Correct colors for all elements", "INFO")
    
    print_colored("\n🚀 Starting bot to test all fixes...", "INFO")
    print_colored("Watch for:", "WARNING")
    print_colored("• Light gray connection messages", "LIGHT_GRAY")
    print_colored("• Teal signal headers", "TEAL")
    print_colored("• Crimson pairs/timeframe/strategy/amount", "CRIMSON")
    print_colored("• Royal purple trading configuration", "ROYAL_PURPLE")
    print_colored("• Burnt orange live option", "BURNT_ORANGE")
    print_colored("• Clean investment amount setting", "INFO")
    print_colored("• Successful trade execution", "SUCCESS")
    
    try:
        # Import and run the main bot
        from Model import main as bot_main
        await bot_main()
        
    except KeyboardInterrupt:
        print_colored("\n🛑 Test completed", "WARNING")
        print_colored("Check all fixes above for proper implementation", "INFO")
    except Exception as e:
        print_colored(f"❌ Test error: {e}", "ERROR")

if __name__ == "__main__":
    asyncio.run(main())
