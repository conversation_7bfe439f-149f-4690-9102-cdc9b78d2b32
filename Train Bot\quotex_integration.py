#!/usr/bin/env python3
"""
Enhanced PyQuotex Integration for Trading Bot
Includes WebSocket connection, real-time data fetching, and automatic trade execution
"""

import asyncio
import time
import json
import re
from datetime import datetime, timedelta
from playwright.async_api import async_playwright
import pandas as pd
import numpy as np
import logging

# Import PyQuotex modules
import sys
import os
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from pyquotex.http.login import Login
from pyquotex.http.settings import Settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SelectorCache:
    """Intelligent selector cache to remember working selectors"""

    def __init__(self, cache_file="selector_cache.json"):
        self.cache_file = cache_file
        self.cache = {
            "login": {
                "email": None,
                "password": None,
                "login_button": None
            },
            "trading": {
                "call_button": ".call-btn",  # Default from user feedback
                "put_button": ".put-btn",    # Working selector from user's log
                "asset_dropdown": None,
                "asset_search": None
            },
            "assets": {}  # Store asset-specific selectors
        }
        self.load_cache()

    def load_cache(self):
        """Load cached selectors from file"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    saved_cache = json.load(f)
                    self.cache.update(saved_cache)
                print(f"📋 Loaded selector cache with {len(self.cache.get('assets', {}))} assets")
        except Exception as e:
            print(f"⚠️ Could not load selector cache: {e}")

    def save_cache(self):
        """Save cached selectors to file"""
        try:
            with open(self.cache_file, 'w') as f:
                json.dump(self.cache, f, indent=2)
            print("💾 Selector cache saved")
        except Exception as e:
            print(f"⚠️ Could not save selector cache: {e}")

    def get_login_selector(self, field_type):
        """Get cached login selector"""
        return self.cache["login"].get(field_type)

    def set_login_selector(self, field_type, selector):
        """Cache working login selector"""
        self.cache["login"][field_type] = selector
        self.save_cache()
        print(f"✅ Cached {field_type} selector: {selector}")

    def get_trade_selector(self, action):
        """Get cached trade selector"""
        button_type = "call_button" if action.lower() == "call" else "put_button"
        return self.cache["trading"].get(button_type)

    def set_trade_selector(self, action, selector):
        """Cache working trade selector"""
        button_type = "call_button" if action.lower() == "call" else "put_button"
        self.cache["trading"][button_type] = selector
        self.save_cache()
        print(f"✅ Cached {action.upper()} button selector: {selector}")

    def get_asset_selector(self, asset):
        """Get cached asset selector"""
        return self.cache["assets"].get(asset)

    def set_asset_selector(self, asset, selector):
        """Cache working asset selector"""
        self.cache["assets"][asset] = selector
        self.save_cache()
        print(f"✅ Cached asset selector for {asset}: {selector}")

class QuotexBotIntegration:
    """
    Enhanced PyQuotex integration with WebSocket connection and automatic trading
    Handles authentication, real-time data fetching, and automatic trade execution
    """

    def __init__(self, email, password, demo_mode=True):
        self.email = email
        self.password = password
        self.demo_mode = demo_mode
        self.base_url = "https://market-qx.pro"

        # Initialize intelligent selector cache
        self.selector_cache = SelectorCache()

        # CACHED SELECTORS for instant execution (✅ PROVEN WORKING!)
        self.cached_selectors = {
            'call': [
                '.call-btn',                  # ✅ CONFIRMED WORKING!
                'button:has-text("UP")',      # ✅ CONFIRMED WORKING!
                'button:has-text("ACIMA")',   # Portuguese for UP
                'button:has-text("CALL")',
                'button:has-text("HIGHER")'
            ],
            'put': [
                '.put-btn',                   # ✅ CONFIRMED WORKING!
                'button:has-text("DOWN")',    # Most likely PUT selector
                'button:has-text("ABAIXO")',  # Portuguese for DOWN
                'button:has-text("PUT")',
                'button:has-text("LOWER")'
            ]
        }

        # INSTANT EXECUTION MODE - Skip all searches
        self.instant_mode = True

        # Cache for current selected asset (to avoid re-selection)
        self.current_selected_asset = None
        self.last_asset_switch_time = 0  # Track timing for optimization

        # HTTP API components
        self.api_mock = type('obj', (object,), {
            'lang': 'pt',
            'https_url': self.base_url,
            'host': 'market-qx.pro',
            'resource_path': '.',
            'user_data_dir': '.',
            'username': email,
            'password': password,
            'session_data': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'cookies': None,
                'token': None
            }
        })()

        self.login_module = Login(self.api_mock)
        self.settings_module = Settings(self.api_mock)

        # Connection state
        self.is_authenticated = False
        self.balance_data = {}
        self.candle_cache = {}
        self.cache_timeout = 30  # seconds

        # Enhanced browser automation for WebSocket and trading
        self.browser = None
        self.context = None
        self.page = None
        self.websocket_connected = False
        self.websocket_data = []
        self.tick_data = {}
        self.current_prices = {}

        # Trading state
        self.is_logged_in_browser = False
        self.last_balance_check = 0
        self.current_balance = 0
        
    async def connect(self):
        """Enhanced connection with WebSocket and browser automation"""
        try:
            print("🔐 Connecting to Quotex...")

            # First, try HTTP authentication
            status, message = await self.login_module(self.email, self.password)

            if status:
                print("✅ HTTP authentication successful!")
                self.is_authenticated = True

                # Get cookies for future requests
                if hasattr(self.login_module, 'response') and self.login_module.response:
                    cookies = self.login_module.response.cookies
                    cookie_string = "; ".join([f"{cookie.name}={cookie.value}" for cookie in cookies])
                    self.api_mock.session_data["cookies"] = cookie_string

            # Now establish browser connection with WebSocket
            print("🌐 Establishing browser connection with WebSocket...")
            browser_connected = await self._connect_browser_websocket()

            if browser_connected:
                print("✅ WebSocket connection established!")
                return True
            else:
                print("⚠️ WebSocket connection failed, but HTTP auth succeeded")
                return True  # Still return True as HTTP auth worked

        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False

    async def _connect_browser_websocket(self, max_retries=3):
        """Establish browser connection with WebSocket for real-time data with retry mechanism"""

        for attempt in range(max_retries):
            try:
                print(f"🌐 Browser connection attempt {attempt + 1}/{max_retries}...")

                # Launch browser
                playwright = await async_playwright().start()
                self.browser = await playwright.chromium.launch(
                    headless=False,  # Keep visible for debugging
                    args=['--no-sandbox', '--disable-dev-shm-usage']
                )

                self.context = await self.browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                )

                self.page = await self.context.new_page()

                # Set up WebSocket monitoring
                self._setup_websocket_handlers()

                # Navigate to Quotex
                trade_url = f"{self.base_url}/pt/trade" if self.demo_mode else f"{self.base_url}/pt/trade"
                await self.page.goto(trade_url, timeout=30000)
                await self.page.wait_for_load_state("networkidle", timeout=20000)

                # Login via browser with retry
                login_success = await self._browser_login_with_retry()

                if login_success:
                    # Wait for WebSocket connection
                    await asyncio.sleep(5)
                    print("🔌 WebSocket connection established")
                    self.websocket_connected = True
                    return True
                else:
                    print(f"❌ Browser login failed on attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        print("🔄 Retrying browser connection...")
                        # Close browser before retry
                        try:
                            if self.browser:
                                await self.browser.close()
                        except:
                            pass
                        await asyncio.sleep(3)
                        continue
                    else:
                        return False

            except Exception as e:
                print(f"❌ Browser connection error (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    print("🔄 Retrying browser connection...")
                    # Close browser before retry
                    try:
                        if self.browser:
                            await self.browser.close()
                    except:
                        pass
                    await asyncio.sleep(3)
                    continue
                else:
                    return False

        return False

    async def _browser_login_with_retry(self, max_retries=3):
        """Browser login with retry mechanism"""
        for attempt in range(max_retries):
            try:
                print(f"🔐 Login attempt {attempt + 1}/{max_retries}...")
                login_success = await self._browser_login()

                if login_success:
                    print("✅ Login successful!")
                    return True
                else:
                    print(f"❌ Login failed on attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        print("🔄 Retrying login in 2 seconds...")
                        await asyncio.sleep(2)
                        continue
                    else:
                        print("❌ All login attempts failed")
                        return False

            except Exception as e:
                print(f"❌ Login error (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    print("🔄 Retrying login in 2 seconds...")
                    await asyncio.sleep(2)
                    continue
                else:
                    print("❌ All login attempts failed")
                    return False

        return False

    def _setup_websocket_handlers(self):
        """Set up WebSocket message handlers for real-time data"""
        def handle_websocket(ws):
            def on_framereceived(payload):
                try:
                    if isinstance(payload, bytes):
                        message = payload.decode('utf-8', errors='ignore')
                    else:
                        message = str(payload)

                    # Process real-time tick data
                    self._process_websocket_message(message)

                except Exception as e:
                    logger.debug(f"WebSocket message processing error: {e}")

            ws.on("framereceived", on_framereceived)

        self.page.on("websocket", handle_websocket)

    def _process_websocket_message(self, message):
        """Process WebSocket messages for tick data"""
        try:
            # Look for tick data patterns: ["ASSET_otc", timestamp, price, direction]
            tick_pattern = r'\["([^"]+_otc)",([0-9.]+),([0-9.]+),([01])\]'
            matches = re.findall(tick_pattern, message)

            for match in matches:
                asset, timestamp, price, direction = match
                timestamp = float(timestamp)
                price = float(price)
                direction = int(direction)

                # Store tick data
                if asset not in self.tick_data:
                    self.tick_data[asset] = []

                self.tick_data[asset].append({
                    'timestamp': timestamp,
                    'price': price,
                    'direction': direction,
                    'datetime': datetime.fromtimestamp(timestamp)
                })

                # Update current price
                self.current_prices[asset] = price

                # Keep only recent data (last 1000 ticks)
                if len(self.tick_data[asset]) > 1000:
                    self.tick_data[asset] = self.tick_data[asset][-1000:]

        except Exception as e:
            logger.debug(f"Tick data processing error: {e}")

    async def _browser_login(self):
        """Login via browser automation"""
        try:
            # Check if already logged in
            current_url = self.page.url
            if "trade" in current_url and "login" not in current_url:
                print("✅ Already logged in to browser")
                self.is_logged_in_browser = True
                if self.demo_mode:
                    await self._switch_to_demo_mode()
                return True

            # Wait for page to load completely
            await asyncio.sleep(3)

            # Enhanced email input detection with comprehensive selectors
            email_selectors = [
                'input[name="email"]',
                'input[type="email"]',
                'input[placeholder*="email" i]',
                'input[placeholder*="e-mail" i]',
                'input[placeholder*="Email" i]',
                'input[placeholder*="E-mail" i]',
                'input[id*="email" i]',
                'input[class*="email" i]',
                'input[data-testid*="email" i]',
                'input[autocomplete="email"]',
                'input[autocomplete="username"]',
                'form input[type="text"]',
                'form input:first-of-type',
                '.login-form input[type="text"]',
                '.auth-form input[type="text"]',
                '#email',
                '#username',
                '[data-cy="email"]',
                '[data-cy="username"]'
            ]

            email_input = None

            # Try cached email selector first
            cached_email_selector = self.selector_cache.get_login_selector("email")
            if cached_email_selector:
                print(f"⚡ Using cached email selector: {cached_email_selector}")
                try:
                    email_input = await self.page.wait_for_selector(cached_email_selector, timeout=1000)
                    if email_input and await email_input.is_visible():
                        print("✅ Cached email selector worked!")
                    else:
                        email_input = None
                except:
                    print("⚠️ Cached email selector failed, searching...")
                    email_input = None

            # If cached selector failed, search for email input
            if not email_input:
                print("🔍 Searching for email input field...")
                for i, selector in enumerate(email_selectors):
                    try:
                        email_input = await self.page.wait_for_selector(selector, timeout=2000)
                        if email_input and await email_input.is_visible():
                            print(f"✅ Found email input with selector: {selector}")
                            # Cache the working selector
                            self.selector_cache.set_login_selector("email", selector)
                            break
                    except:
                        continue

            if not email_input:
                print("⚠️ Email input not found with standard selectors, trying alternative approach...")
                # Try to find any visible input field
                try:
                    all_inputs = await self.page.query_selector_all('input')
                    for input_elem in all_inputs:
                        if await input_elem.is_visible():
                            input_type = await input_elem.get_attribute('type')
                            placeholder = await input_elem.get_attribute('placeholder')
                            name = await input_elem.get_attribute('name')

                            if (input_type in ['text', 'email'] or
                                (placeholder and 'email' in placeholder.lower()) or
                                (name and 'email' in name.lower())):
                                email_input = input_elem
                                print(f"✅ Found email input by scanning all inputs")
                                break
                except:
                    pass

            if not email_input:
                print("⚠️ Email input still not found, checking if already logged in...")
                await asyncio.sleep(3)
                current_url = self.page.url
                if "trade" in current_url:
                    print("✅ Appears to be logged in already")
                    self.is_logged_in_browser = True
                    if self.demo_mode:
                        await self._switch_to_demo_mode()
                    return True
                else:
                    print("❌ Email input not found and not on trade page")
                    return False

            # Fill email
            await email_input.fill(self.email)
            await asyncio.sleep(1)

            # Enhanced password input detection
            password_selectors = [
                'input[name="password"]',
                'input[type="password"]',
                'input[placeholder*="password" i]',
                'input[placeholder*="Password" i]',
                'input[placeholder*="senha" i]',
                'input[placeholder*="Senha" i]',
                'input[id*="password" i]',
                'input[class*="password" i]',
                'input[data-testid*="password" i]',
                'input[autocomplete="current-password"]',
                'input[autocomplete="password"]',
                'form input[type="password"]',
                'form input:last-of-type',
                '.login-form input[type="password"]',
                '.auth-form input[type="password"]',
                '#password',
                '[data-cy="password"]'
            ]

            password_input = None

            # Try cached password selector first
            cached_password_selector = self.selector_cache.get_login_selector("password")
            if cached_password_selector:
                print(f"⚡ Using cached password selector: {cached_password_selector}")
                try:
                    password_input = await self.page.wait_for_selector(cached_password_selector, timeout=1000)
                    if password_input and await password_input.is_visible():
                        print("✅ Cached password selector worked!")
                    else:
                        password_input = None
                except:
                    print("⚠️ Cached password selector failed, searching...")
                    password_input = None

            # If cached selector failed, search for password input
            if not password_input:
                print("🔍 Searching for password input field...")

                # Wait a moment for password field to appear after email input
                await asyncio.sleep(1)

                for selector in password_selectors:
                    try:
                        password_input = await self.page.wait_for_selector(selector, timeout=2000)
                        if password_input and await password_input.is_visible():
                            print(f"✅ Found password input with selector: {selector}")
                            # Cache the working selector
                            self.selector_cache.set_login_selector("password", selector)
                            break
                    except:
                        continue

            if not password_input:
                print("⚠️ Password input not found with standard selectors, trying intelligent scanning...")
                # Try to find any password input field by scanning all inputs
                try:
                    all_inputs = await self.page.query_selector_all('input')
                    for input_elem in all_inputs:
                        if await input_elem.is_visible():
                            input_type = await input_elem.get_attribute('type')
                            placeholder = await input_elem.get_attribute('placeholder')
                            name = await input_elem.get_attribute('name')
                            id_attr = await input_elem.get_attribute('id')

                            if (input_type == 'password' or
                                (placeholder and 'password' in placeholder.lower()) or
                                (name and 'password' in name.lower()) or
                                (id_attr and 'password' in id_attr.lower())):
                                password_input = input_elem
                                print(f"✅ Found password input by intelligent scanning")
                                break
                except:
                    pass

            if not password_input:
                print("❌ Password input still not found after intelligent scanning")
                return False

            # Fill password
            print("🔑 Filling password...")
            await password_input.fill(self.password)
            await asyncio.sleep(1)

            # Find and click login button (more reliable than Enter key)
            login_button_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Login")',
                'button:has-text("Sign In")',
                'button:has-text("Enter")',
                'button:has-text("Entrar")',
                'button:has-text("Log In")',
                '.login-btn',
                '.signin-btn',
                '.submit-btn',
                '#login-btn',
                '#signin-btn',
                '#submit-btn',
                '[data-testid="login"]',
                '[data-testid="signin"]',
                'form button',
                '.btn-primary',
                '.btn-success'
            ]

            login_button = None

            # Try cached login button selector first
            cached_login_selector = self.selector_cache.get_login_selector("login_button")
            if cached_login_selector:
                print(f"⚡ Using cached login button selector: {cached_login_selector}")
                try:
                    login_button = await self.page.wait_for_selector(cached_login_selector, timeout=1000)
                    if login_button and await login_button.is_visible():
                        await login_button.click()
                        print("✅ Cached login button worked!")
                    else:
                        login_button = None
                except:
                    print("⚠️ Cached login button failed, searching...")
                    login_button = None

            # If cached selector failed, search for login button
            if not login_button:
                print("🔍 Searching for login button...")
                for selector in login_button_selectors:
                    try:
                        login_button = await self.page.wait_for_selector(selector, timeout=1000)
                        if login_button and await login_button.is_visible():
                            print(f"✅ Found login button with selector: {selector}")
                            # Cache the working selector
                            self.selector_cache.set_login_selector("login_button", selector)
                            await login_button.click()
                            print("🚀 Login button clicked!")
                            break
                    except:
                        continue

            if not login_button:
                print("⚠️ Login button not found, trying Enter key...")
                await password_input.press('Enter')

            # Wait for login to complete
            print("⏳ Waiting for login to complete...")
            await asyncio.sleep(5)
            await self.page.wait_for_load_state("networkidle", timeout=20000)

            # Check if login was successful
            current_url = self.page.url
            if "trade" in current_url and "login" not in current_url:
                print("✅ Browser login successful")
                self.is_logged_in_browser = True

                # Switch to demo mode if needed
                if self.demo_mode:
                    await self._switch_to_demo_mode()

                return True
            else:
                print("❌ Browser login failed - still on login page")
                return False

        except Exception as e:
            print(f"❌ Browser login error: {e}")
            return False

    async def _switch_to_demo_mode(self):
        """INSTANT demo mode detection - no searching"""
        try:
            print("⚡ INSTANT demo mode check...")

            # INSTANT CHECK: If balance is $10,000, we're in demo mode
            try:
                balance = await self.get_balance()
                if balance == 10000.0:
                    print("✅ INSTANT: Demo mode confirmed (balance: $10,000)")
                    return True
            except:
                pass

            # INSTANT: Assume demo mode (skip all searching)
            print("✅ INSTANT: Assuming demo mode (skipping search)")
            return True

        except Exception as e:
            print(f"❌ Demo mode check error: {e}")
            return True  # Always assume demo mode for speed

    async def _verify_demo_mode(self):
        """Verify that demo mode is active"""
        try:
            # Look for demo mode indicators
            demo_indicators = [
                'text="Demo"',
                'text="DEMO"',
                'text="Practice"',
                'text="PRACTICE"',
                'text="Treino"',
                'text="$10,000"',
                'text="$10000"',
                '.demo-indicator',
                '.practice-indicator',
                '[data-mode="demo"]'
            ]

            for indicator in demo_indicators:
                try:
                    element = await self.page.wait_for_selector(indicator, timeout=1000)
                    if element and await element.is_visible():
                        return True
                except:
                    continue

            return False

        except Exception as e:
            print(f"❌ Demo mode verification error: {e}")
            return False
    
    async def get_balance(self):
        """Get current account balance"""
        try:
            if not self.is_authenticated:
                await self.connect()
            
            settings_data = self.settings_module.get_settings()
            
            if isinstance(settings_data, dict) and 'data' in settings_data:
                account_data = settings_data['data']
                
                self.balance_data = {
                    'live_balance': float(account_data.get('liveBalance', 0)),
                    'demo_balance': float(account_data.get('demoBalance', 0)),
                    'currency': account_data.get('currencyCode', 'USD'),
                    'user_id': account_data.get('nickname', 'Unknown'),
                    'email': account_data.get('email', 'Unknown'),
                    'timestamp': time.time()
                }
                
                current_balance = self.balance_data['demo_balance'] if self.demo_mode else self.balance_data['live_balance']
                return current_balance
            
            return 0.0
            
        except Exception as e:
            print(f"❌ Error getting balance: {e}")
            return 0.0
    
    def change_account(self, account_type):
        """Change account type (PRACTICE/REAL)"""
        if account_type == "PRACTICE":
            self.demo_mode = True
            print("🔵 Switched to Demo account")
        elif account_type == "REAL":
            self.demo_mode = False
            print("🔴 Switched to Live account")
    
    async def get_candles_browser(self, asset, period=60, count=100):
        """Get candle data using real-time WebSocket data"""
        try:
            # Check cache first
            cache_key = f"{asset}_{period}_{count}"
            if cache_key in self.candle_cache:
                cache_time, cached_data = self.candle_cache[cache_key]
                if time.time() - cache_time < self.cache_timeout:
                    return cached_data

            print(f"📊 Fetching candle data for {asset} from WebSocket...")

            # Use real-time tick data to build candles
            candle_data = self._build_candles_from_ticks(asset, period, count)

            if candle_data and len(candle_data) > 0:
                # Cache the data
                self.candle_cache[cache_key] = (time.time(), candle_data)
                print(f"✅ Retrieved {len(candle_data)} candles for {asset} from WebSocket")
                return candle_data
            else:
                print(f"⚠️ No WebSocket data available for {asset}, waiting for data...")
                # Wait a bit for data to accumulate
                await asyncio.sleep(5)

                # Try again
                candle_data = self._build_candles_from_ticks(asset, period, count)
                if candle_data and len(candle_data) > 0:
                    self.candle_cache[cache_key] = (time.time(), candle_data)
                    print(f"✅ Retrieved {len(candle_data)} candles for {asset} after waiting")
                    return candle_data
                else:
                    print(f"❌ No candle data available for {asset}")
                    return None

        except Exception as e:
            print(f"❌ Error getting candles for {asset}: {e}")
            return None

    def _build_candles_from_ticks(self, asset, period=60, count=100):
        """Build OHLC candles from tick data"""
        try:
            if asset not in self.tick_data or len(self.tick_data[asset]) == 0:
                return None

            ticks = self.tick_data[asset]
            if len(ticks) < 10:  # Need minimum ticks
                return None

            # Sort ticks by timestamp
            ticks = sorted(ticks, key=lambda x: x['timestamp'])

            # Build candles
            candles = []
            current_candle = None

            for tick in ticks:
                timestamp = tick['timestamp']
                price = tick['price']

                # Calculate candle start time
                candle_start = int(timestamp // period) * period

                if current_candle is None or current_candle['time'] != candle_start:
                    # Save previous candle
                    if current_candle is not None:
                        candles.append(current_candle)

                    # Start new candle
                    current_candle = {
                        'time': candle_start,
                        'open': price,
                        'high': price,
                        'low': price,
                        'close': price,
                        'volume': 1
                    }
                else:
                    # Update existing candle
                    current_candle['high'] = max(current_candle['high'], price)
                    current_candle['low'] = min(current_candle['low'], price)
                    current_candle['close'] = price
                    current_candle['volume'] += 1

            # Add final candle
            if current_candle is not None:
                candles.append(current_candle)

            # Return last 'count' candles
            return candles[-count:] if len(candles) > count else candles

        except Exception as e:
            print(f"❌ Error building candles from ticks: {e}")
            return None
    
    async def _fetch_candles_with_browser(self, asset, period=60, count=100):
        """Fetch candle data using browser automation"""
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                )
                page = await context.new_page()
                
                # Capture WebSocket data
                candle_data = []
                tick_data = []
                
                def handle_websocket(ws):
                    def on_framereceived(payload):
                        try:
                            if isinstance(payload, bytes):
                                message = payload.decode('utf-8', errors='ignore')
                            else:
                                message = str(payload)
                            
                            # Look for tick data: ["ASSET_otc", timestamp, price, direction]
                            if asset in message and re.search(r'\d+\.\d{4,}', message):
                                match = re.search(rf'\["{asset}",([0-9.]+),([0-9.]+),([01])\]', message)
                                if match:
                                    timestamp = float(match.group(1))
                                    price = float(match.group(2))
                                    direction = int(match.group(3))
                                    
                                    tick_data.append({
                                        'timestamp': timestamp,
                                        'price': price,
                                        'direction': direction
                                    })
                        except:
                            pass
                    
                    ws.on("framereceived", on_framereceived)
                
                page.on("websocket", handle_websocket)
                
                # Navigate and login
                await page.goto(f"{self.base_url}/pt/trade", timeout=30000)
                await page.wait_for_load_state("networkidle", timeout=20000)
                
                # Quick login if needed
                email_input = await page.query_selector('input[name="email"], input[type="email"]')
                if email_input and await email_input.is_visible():
                    await email_input.fill(self.email)
                    password_input = await page.query_selector('input[name="password"], input[type="password"]')
                    if password_input:
                        await password_input.fill(self.password)
                        await password_input.press('Enter')
                        await page.wait_for_load_state("networkidle", timeout=20000)
                
                # Wait for WebSocket connection and collect data
                await asyncio.sleep(10)
                
                # Try to select the asset
                asset_selectors = [f'text={asset.replace("_otc", "")}', f'[data-asset="{asset}"]']
                for selector in asset_selectors:
                    try:
                        element = await page.query_selector(selector)
                        if element:
                            await element.click()
                            await asyncio.sleep(2)
                            break
                    except:
                        continue
                
                # Collect more data
                await asyncio.sleep(15)
                
                await browser.close()
                
                # Convert tick data to candles
                if tick_data:
                    candle_data = self._convert_ticks_to_candles(tick_data, period)
                    return candle_data[-count:] if len(candle_data) > count else candle_data
                
                return None
                
        except Exception as e:
            print(f"❌ Browser candle fetch error: {e}")
            return None
    
    def _convert_ticks_to_candles(self, tick_data, period=60):
        """Convert tick data to OHLC candles"""
        if not tick_data:
            return []
        
        # Sort by timestamp
        tick_data.sort(key=lambda x: x['timestamp'])
        
        candles = []
        current_candle = None
        
        for tick in tick_data:
            timestamp = tick['timestamp']
            price = tick['price']
            
            # Calculate candle start time
            candle_start = int(timestamp // period) * period
            
            if current_candle is None or current_candle['time'] != candle_start:
                # Save previous candle
                if current_candle is not None:
                    candles.append(current_candle)
                
                # Start new candle
                current_candle = {
                    'time': candle_start,
                    'open': price,
                    'high': price,
                    'low': price,
                    'close': price,
                    'volume': 1
                }
            else:
                # Update existing candle
                current_candle['high'] = max(current_candle['high'], price)
                current_candle['low'] = min(current_candle['low'], price)
                current_candle['close'] = price
                current_candle['volume'] += 1
        
        # Add final candle
        if current_candle is not None:
            candles.append(current_candle)
        
        return candles
    
    async def trade(self, action, amount, asset, duration):
        """⚡ INTELLIGENT TRADE EXECUTION - Cached selectors + asset-specific trading"""
        try:
            if not self.page:
                return False, f"Failed to place trade on {asset} in {action.upper()} direction"

            start_time = time.time()

            # STEP 1: Ensure correct asset is selected for this specific trade
            asset_selected = await self._select_specific_asset(asset)
            if not asset_selected:
                print(f"⚠️ Could not select {asset}, continuing with current asset")

            # STEP 2: INTELLIGENT trade execution with cached selectors
            print(f"⚡ Executing {action.upper()} trade on {asset} using intelligent cache...")

            # Try cached selector first for instant execution
            cached_selector = self.selector_cache.get_trade_selector(action)
            success = False

            if cached_selector:
                print(f"⚡ Using cached {action.upper()} selector: {cached_selector}")
                try:
                    element = await self.page.wait_for_selector(cached_selector, timeout=500)
                    if element and await element.is_visible():
                        await element.click()
                        print(f"✅ Cached {action.upper()} selector worked instantly!")
                        success = True
                    else:
                        print(f"⚠️ Cached {action.upper()} selector failed, searching...")
                except Exception as e:
                    print(f"⚠️ Cached {action.upper()} selector error: {e}")

            # If cached selector failed, search for working selector
            if not success:
                success = await self._search_and_cache_trade_button(action)

            total_time = time.time() - start_time

            if success:
                print(f"🎉 Trade executed successfully in {total_time:.2f}s")
                return True, f"Successfully placed trade on {asset} in {action.upper()} direction"
            else:
                print(f"❌ All trade selectors failed for {action.upper()} on {asset}")
                return False, f"Failed to place trade on {asset} in {action.upper()} direction"

        except Exception as e:
            print(f"❌ Trade execution error: {e}")
            return False, f"Failed to place trade on {asset} in {action.upper()} direction"

    async def _select_specific_asset(self, asset):
        """Select specific asset for trading"""
        try:
            display_asset = asset.replace("_otc", "")
            print(f"🎯 Selecting asset: {display_asset}")

            # Try cached asset selector first
            cached_asset_selector = self.selector_cache.get_asset_selector(asset)
            if cached_asset_selector:
                print(f"⚡ Using cached asset selector: {cached_asset_selector}")
                try:
                    element = await self.page.wait_for_selector(cached_asset_selector, timeout=500)
                    if element and await element.is_visible():
                        await element.click()
                        print(f"✅ Cached asset selector worked for {asset}")
                        return True
                except:
                    print(f"⚠️ Cached asset selector failed for {asset}")

            # Search for asset selector
            asset_selectors = [
                f'button:has-text("{display_asset}")',
                f'[data-asset="{display_asset}"]',
                f'[data-symbol="{display_asset}"]',
                f'[title="{display_asset}"]',
                f'span:has-text("{display_asset}")',
                f'div:has-text("{display_asset}")'
            ]

            for selector in asset_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=300)
                    if element and await element.is_visible():
                        await element.click()
                        print(f"✅ Selected {asset} with selector: {selector}")
                        # Cache the working selector
                        self.selector_cache.set_asset_selector(asset, selector)
                        return True
                except:
                    continue

            print(f"⚠️ Could not find asset selector for {asset}")
            return False

        except Exception as e:
            print(f"❌ Asset selection error: {e}")
            return False

    async def _search_and_cache_trade_button(self, action):
        """Search for trade button and cache working selector"""
        try:
            print(f"🔍 Searching for {action.upper()} trade button...")

            if action.lower() == 'call':
                # Prioritized CALL selectors based on user feedback
                trade_selectors = [
                    '.call-btn',  # Known working selector
                    'button:has-text("UP")',
                    'button:has-text("CALL")',
                    'button:has-text("HIGHER")',
                    'button:has-text("BUY")',
                    '.up-btn',
                    '.buy-btn',
                    '#call-btn',
                    '[data-direction="call"]',
                    '[data-action="buy"]',
                    'button[style*="green"]',
                    '.btn-success',
                    '.trading-buttons button:first-child'
                ]
            else:
                # Prioritized PUT selectors based on user feedback
                trade_selectors = [
                    '.put-btn',   # Known working selector from user's log
                    'button:has-text("DOWN")',
                    'button:has-text("PUT")',
                    'button:has-text("LOWER")',
                    'button:has-text("SELL")',
                    '.down-btn',
                    '.sell-btn',
                    '#put-btn',
                    '[data-direction="put"]',
                    '[data-action="sell"]',
                    'button[style*="red"]',
                    '.btn-danger',
                    '.trading-buttons button:last-child'
                ]

            # Try each selector with fast timeout
            for i, selector in enumerate(trade_selectors):
                try:
                    print(f"   Trying selector {i+1}/{len(trade_selectors)}: {selector}")
                    element = await self.page.wait_for_selector(selector, timeout=500)
                    if element and await element.is_visible():
                        await element.click()
                        print(f"   ✅ Successfully clicked {action.upper()} button with selector: {selector}")
                        # Cache the working selector for future use
                        self.selector_cache.set_trade_selector(action, selector)
                        return True
                except:
                    continue

            print(f"❌ No working {action.upper()} button selector found")
            return False

        except Exception as e:
            print(f"❌ Trade button search error: {e}")
            return False

            if action.lower() == 'call':
                # Comprehensive CALL/UP selectors for maximum compatibility
                trade_selectors = [
                    # Text-based selectors
                    'button:has-text("UP")',
                    'button:has-text("CALL")',
                    'button:has-text("HIGHER")',
                    'button:has-text("BUY")',
                    'button:has-text("↑")',
                    'button:has-text("▲")',
                    # Class-based selectors
                    '.call-btn',
                    '.up-btn',
                    '.buy-btn',
                    '.higher-btn',
                    '.green-btn',
                    # ID-based selectors
                    '#call-btn',
                    '#up-btn',
                    '#buy-btn',
                    # Data attribute selectors
                    '[data-direction="call"]',
                    '[data-direction="up"]',
                    '[data-action="call"]',
                    '[data-action="buy"]',
                    '[data-type="call"]',
                    '[data-type="up"]',
                    # Color-based selectors
                    'button[style*="green"]',
                    'button.green',
                    '.btn-success',
                    '.btn-call',
                    # Generic button selectors with position
                    '.trading-buttons button:first-child',
                    '.trade-buttons button:first-child',
                    '.option-buttons button:first-child'
                ]
            else:
                # Comprehensive PUT/DOWN selectors for maximum compatibility
                trade_selectors = [
                    # Text-based selectors
                    'button:has-text("DOWN")',
                    'button:has-text("PUT")',
                    'button:has-text("LOWER")',
                    'button:has-text("SELL")',
                    'button:has-text("↓")',
                    'button:has-text("▼")',
                    # Class-based selectors
                    '.put-btn',
                    '.down-btn',
                    '.sell-btn',
                    '.lower-btn',
                    '.red-btn',
                    # ID-based selectors
                    '#put-btn',
                    '#down-btn',
                    '#sell-btn',
                    # Data attribute selectors
                    '[data-direction="put"]',
                    '[data-direction="down"]',
                    '[data-action="put"]',
                    '[data-action="sell"]',
                    '[data-type="put"]',
                    '[data-type="down"]',
                    # Color-based selectors
                    'button[style*="red"]',
                    'button.red',
                    '.btn-danger',
                    '.btn-put',
                    # Generic button selectors with position
                    '.trading-buttons button:last-child',
                    '.trade-buttons button:last-child',
                    '.option-buttons button:last-child'
                ]

            success = False
            print(f"🎯 Attempting to execute {action.upper()} trade on {asset}...")

            # Try each selector with optimized timeout for speed
            for i, selector in enumerate(trade_selectors):
                try:
                    print(f"   Trying selector {i+1}/{len(trade_selectors)}: {selector}")
                    element = await self.page.wait_for_selector(selector, timeout=800)  # Faster timeout
                    if element and await element.is_visible():
                        await element.click()
                        print(f"   ✅ Successfully clicked trade button with selector: {selector}")
                        success = True
                        break
                except Exception as e:
                    # Don't print every failure to reduce noise
                    if i < 5:  # Only print first few failures
                        print(f"   ❌ Selector failed: {selector}")
                    continue

            total_time = time.time() - start_time

            if success:
                print(f"🎉 Trade executed successfully in {total_time:.2f}s")
                return True, f"Successfully placed trade on {asset} in {action.upper()} direction"
            else:
                print(f"❌ All {len(trade_selectors)} trade selectors failed for {action.upper()} on {asset}")
                print(f"   Current page URL: {self.page.url}")
                print(f"   Page title: {await self.page.title()}")

                # Try to get page content for debugging
                try:
                    buttons = await self.page.query_selector_all('button')
                    print(f"   Found {len(buttons)} buttons on page")

                    # Show first few button texts for debugging
                    for i, button in enumerate(buttons[:5]):
                        try:
                            text = await button.inner_text()
                            if text.strip():
                                print(f"   Button {i+1}: '{text.strip()}'")
                        except:
                            pass
                except:
                    pass

                return False, f"Failed to place trade on {asset} in {action.upper()} direction"

        except Exception as e:
            print(f"❌ Trade execution error: {e}")
            return False, f"Failed to place trade on {asset} in {action.upper()} direction"

    async def _ensure_correct_trading_setup(self, asset):
        """⚡ INSTANT SETUP - Minimal navigation for maximum speed"""
        try:
            current_time = time.time()

            # INSTANT: Navigate to trading page only if absolutely necessary
            current_url = self.page.url
            if "trade" not in current_url:
                print(f"🔄 Navigating to trading page for {asset}...")
                await self.page.goto(f"{self.base_url}/pt/trade", timeout=3000)
                await asyncio.sleep(0.5)  # Minimal wait for page load

            # INSTANT SUCCESS: Mark asset as ready
            self.current_selected_asset = asset
            self.last_asset_switch_time = current_time
            print(f"✅ Trading setup ready for {asset}")

            return True

        except Exception as e:
            print(f"⚠️ Setup error for {asset}: {e}")
            return True  # Continue anyway to avoid blocking trades

    async def _instant_trade_execution(self, action):
        """🚀 ULTRA-AGGRESSIVE trade execution - SILENT operation"""
        try:
            # Use MOST RELIABLE selectors with ultra-minimal timeouts
            if action.lower() == 'call':
                selectors = ['button:has-text("UP")', '.call-btn', 'button:has-text("CALL")']
            else:
                selectors = ['button:has-text("DOWN")', '.put-btn', 'button:has-text("PUT")']

            # Try each selector with ULTRA-MINIMAL timeout
            for selector in selectors:
                try:
                    await self.page.click(selector, timeout=50)  # 50ms only!
                    return True  # INSTANT success
                except:
                    continue

            return False  # SILENT failure

        except Exception as e:
            return False  # SILENT error handling

            if trade_success:
                # Wait a moment for trade to process
                await asyncio.sleep(3)

                # Check balance after trade
                balance_after = await self.get_balance()
                print(f"💰 Balance after trade: ${balance_after:.2f}")

                # Verify trade was executed (balance should be reduced by trade amount)
                if balance_after < balance_before:
                    balance_diff = balance_before - balance_after
                    print(f"✅ Trade executed successfully! Balance reduced by ${balance_diff:.2f}")
                    return True, {
                        'status': 'executed',
                        'balance_before': balance_before,
                        'balance_after': balance_after,
                        'amount_deducted': balance_diff,
                        'asset': asset,
                        'direction': action,
                        'amount': amount,
                        'duration': duration
                    }
                else:
                    print("⚠️ Trade may not have been executed (balance unchanged)")
                    return False, "Trade execution uncertain - balance unchanged"
            else:
                print("❌ Trade execution failed")
                return False, "Browser trade execution failed"

        except Exception as e:
            print(f"❌ Trade execution error: {e}")
            return False, str(e)

    async def _ultra_fast_trade(self, action, amount=10.0, asset="EURUSD", duration=60):
        """INSTANT trade execution - no searching, direct clicking"""
        try:
            print(f"⚡ INSTANT {action.upper()} EXECUTION...")

            # INSTANT EXECUTION: Use coordinate-based clicking (fastest method)
            success = await self._instant_coordinate_click(action)

            if success:
                print(f"⚡ INSTANT {action.upper()} TRADE EXECUTED!")
                return True

            # FALLBACK: Try one quick selector
            print("⚠️ Coordinate click failed, trying quick selector...")
            success = await self._quick_selector_click(action)

            if success:
                print(f"⚡ QUICK {action.upper()} TRADE EXECUTED!")
                return True

            print(f"⚠️ All instant methods failed for {action.upper()}")
            return False

        except Exception as e:
            print(f"❌ Instant trade error: {e}")
            return False

    async def _instant_coordinate_click(self, action):
        """Instant coordinate-based clicking (fastest possible)"""
        try:
            # Get page dimensions
            viewport = self.page.viewport_size
            if callable(viewport):
                viewport = await viewport()
            width = viewport['width']
            height = viewport['height']

            # Typical trade button locations (based on common Quotex layouts)
            if action.lower() == 'call':
                # CALL button typically on the left or bottom-left
                coordinates = [
                    (width * 0.3, height * 0.8),  # Bottom-left area
                    (width * 0.2, height * 0.7),  # Left-center
                    (width * 0.4, height * 0.9),  # Bottom-center-left
                ]
            else:  # PUT
                # PUT button typically on the right or bottom-right
                coordinates = [
                    (width * 0.7, height * 0.8),  # Bottom-right area
                    (width * 0.8, height * 0.7),  # Right-center
                    (width * 0.6, height * 0.9),  # Bottom-center-right
                ]

            # Try each coordinate
            for x, y in coordinates:
                try:
                    await self.page.click(x, y, timeout=100)
                    print(f"⚡ INSTANT coordinate click: ({x:.0f}, {y:.0f})")
                    return True
                except:
                    continue

            return False

        except Exception as e:
            print(f"❌ Coordinate click error: {e}")
            return False

    async def _quick_selector_click(self, action):
        """INSTANT cached selector click"""
        try:
            # Use cached selectors (prioritize working ones)
            selectors = self.cached_selectors.get(action.lower(), [])

            # Try each cached selector with minimal timeout
            for selector in selectors:
                try:
                    await self.page.click(selector, timeout=200)  # Very fast timeout
                    print(f"⚡ INSTANT cached selector success: {selector}")
                    return True
                except:
                    continue

            print(f"⚠️ No cached selectors worked for {action}")
            return False

        except Exception as e:
            print(f"❌ Quick selector error: {e}")
            return False

    async def _execute_browser_trade(self, action, amount, asset, duration):
        """INSTANT browser trade execution - direct button click only"""
        try:
            print(f"⚡ INSTANT BROWSER {action.upper()}...")

            # SKIP ALL SETUP - Go DIRECTLY to trade button click
            trade_executed = await self._click_trade_button(action)
            if not trade_executed:
                print("❌ Failed to execute trade")
                return False

            print(f"✅ Trade execution completed: {action.upper()} {asset} ${amount}")
            return True

        except Exception as e:
            print(f"❌ Browser trade execution error: {e}")
            return False

    async def _select_asset(self, asset):
        """⚡ SILENT asset selection - optimized for speed"""
        try:
            # Remove _otc suffix for display
            display_asset = asset.replace("_otc", "")

            # Minimal wait for page readiness
            await asyncio.sleep(0.1)

            # Enhanced asset selectors
            asset_selectors = [
                # Direct asset selectors
                f'[data-asset="{asset}"]',
                f'[data-asset="{display_asset}"]',
                f'[data-symbol="{asset}"]',
                f'[data-symbol="{display_asset}"]',
                f'[data-pair="{asset}"]',
                f'[data-pair="{display_asset}"]',
                f'[data-currency="{display_asset}"]',
                f'[value="{asset}"]',
                f'[value="{display_asset}"]',

                # Text-based selectors
                f'button:has-text("{display_asset}")',
                f'div:has-text("{display_asset}")',
                f'span:has-text("{display_asset}")',
                f'li:has-text("{display_asset}")',
                f'option:has-text("{display_asset}")',
                f'a:has-text("{display_asset}")',

                # Class-based selectors
                '.asset-item',
                '.currency-item',
                '.pair-item',
                '.symbol-item',
                '.trading-pair',
                '.currency-pair',
                '.asset-button',
                '.pair-button',
                '.symbol-button',

                # Dropdown and select elements
                f'select option[value="{asset}"]',
                f'select option[value="{display_asset}"]',
                f'select option:has-text("{display_asset}")',

                # Generic selectors
                '.asset-selector button',
                '.pair-selector button',
                '.currency-selector button',
                '.trading-assets button',
                '.assets-list button',
                '.pairs-list button'
            ]

            # Try each selector - SILENT operation with reduced timeouts
            for selector in asset_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=300)  # Reduced from 2000ms
                    if element and await element.is_visible():
                        await element.click()
                        await asyncio.sleep(0.1)  # Reduced from 2s

                        # Quick verification
                        if await self._verify_asset_selection(asset):
                            return True
                        else:
                            continue
                except:
                    continue

            # Try dropdown approach - SILENT
            dropdown_selectors = [
                'select[name="asset"]',
                'select[name="pair"]',
                'select[name="symbol"]',
                'select[name="currency"]',
                '.asset-dropdown',
                '.pair-dropdown',
                '.currency-dropdown'
            ]

            for selector in dropdown_selectors:
                try:
                    dropdown = await self.page.wait_for_selector(selector, timeout=300)  # Reduced timeout
                    if dropdown and await dropdown.is_visible():
                        await dropdown.select_option(value=asset)
                        await asyncio.sleep(0.1)  # Reduced from 2s
                        return True
                except:
                    try:
                        await dropdown.select_option(value=display_asset)
                        await asyncio.sleep(0.1)  # Reduced from 2s
                        return True
                    except:
                        continue

            # Try search approach - SILENT
            search_selectors = [
                'input[placeholder*="search" i]',
                'input[placeholder*="asset" i]',
                'input[placeholder*="pair" i]',
                'input[name="search"]',
                '.search-input',
                '.asset-search',
                '.pair-search'
            ]

            for selector in search_selectors:
                try:
                    search_input = await self.page.wait_for_selector(selector, timeout=300)  # Reduced timeout
                    if search_input and await search_input.is_visible():
                        await search_input.fill(display_asset)
                        await asyncio.sleep(0.1)  # Reduced from 1s
                        await search_input.press('Enter')
                        await asyncio.sleep(0.2)  # Reduced from 2s
                        return True
                except:
                    continue

            # SILENT assumption of success
            return True

        except Exception as e:
            return False  # SILENT failure

    async def _verify_asset_selection(self, asset):
        """Verify that the correct asset is selected"""
        try:
            display_asset = asset.replace("_otc", "")

            # Look for selected asset indicators
            verification_selectors = [
                f'text="{asset}"',
                f'text="{display_asset}"',
                f'[data-selected-asset="{asset}"]',
                f'[data-selected-asset="{display_asset}"]',
                '.selected-asset',
                '.current-asset',
                '.active-asset'
            ]

            for selector in verification_selectors:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=1000)
                    if element and await element.is_visible():
                        return True
                except:
                    continue

            return False

        except Exception as e:
            return False  # SILENT failure

    async def _set_trade_amount(self, amount):
        """Set trade amount in browser with enhanced selectors"""
        try:
            print(f"💰 Setting trade amount: ${amount}")

            # Enhanced amount input selectors
            amount_selectors = [
                # Common input selectors
                'input[data-testid="amount-input"]',
                'input[data-testid="trade-amount"]',
                'input[name="amount"]',
                'input[name="trade_amount"]',
                'input[name="investment"]',
                'input[placeholder*="amount" i]',
                'input[placeholder*="valor" i]',  # Portuguese
                'input[placeholder*="investment" i]',
                'input[type="number"]',

                # Class-based selectors
                '.amount-input input',
                '.trade-amount input',
                '.investment-input input',
                '.amount-field input',
                '.trade-field input',
                '.input-amount',
                '.input-trade-amount',

                # Generic input selectors
                '.trading-panel input[type="number"]',
                '.trade-panel input[type="number"]',
                '.amount-panel input',
                '.investment-panel input',

                # Fallback selectors
                'input[step]',  # Number inputs often have step attribute
                'input[min]',   # Number inputs often have min attribute
            ]

            for selector in amount_selectors:
                try:
                    print(f"🔍 Trying amount selector: {selector}")
                    amount_input = await self.page.wait_for_selector(selector, timeout=2000)
                    if amount_input and await amount_input.is_visible():
                        # Clear and set amount
                        await amount_input.click()
                        await amount_input.select_all()  # Select all text
                        await amount_input.press('Delete')  # Delete selected text
                        await amount_input.type(str(amount))
                        await asyncio.sleep(1)

                        # Verify the amount was set
                        current_value = await amount_input.input_value()
                        if str(amount) in current_value:
                            print(f"✅ Successfully set trade amount: ${amount}")
                            return True
                        else:
                            print(f"⚠️ Amount not set correctly: expected {amount}, got {current_value}")
                except Exception as e:
                    print(f"⚠️ Amount selector {selector} failed: {e}")
                    continue

            # Try alternative method: look for amount buttons
            print(f"🔍 Looking for amount buttons...")
            amount_buttons = [
                f'button:has-text("${amount}")',
                f'button:has-text("{amount}")',
                f'button[data-amount="{amount}"]',
                f'button[value="{amount}"]'
            ]

            for selector in amount_buttons:
                try:
                    button = await self.page.wait_for_selector(selector, timeout=2000)
                    if button and await button.is_visible():
                        await button.click()
                        await asyncio.sleep(1)
                        print(f"✅ Set amount via button: ${amount}")
                        return True
                except:
                    continue

            print(f"⚠️ Amount input not found, using default amount")
            return True

        except Exception as e:
            print(f"❌ Amount setting error: {e}")
            return False

    async def _set_duration(self, duration):
        """Set trade duration in browser with enhanced selectors"""
        try:
            print(f"⏰ Setting trade duration: {duration}s")

            # Convert duration to different formats
            duration_minutes = duration // 60
            duration_text_options = [
                f"{duration}s",
                f"{duration} s",
                f"{duration}sec",
                f"{duration} sec",
                f"{duration} seconds",
                f"{duration_minutes}m" if duration_minutes > 0 else None,
                f"{duration_minutes} m" if duration_minutes > 0 else None,
                f"{duration_minutes}min" if duration_minutes > 0 else None,
                f"{duration_minutes} min" if duration_minutes > 0 else None,
                f"{duration_minutes} minutes" if duration_minutes > 0 else None,
            ]

            # Remove None values
            duration_text_options = [opt for opt in duration_text_options if opt is not None]

            # Enhanced duration selectors
            duration_selectors = [
                # Data attribute selectors
                f'button[data-duration="{duration}"]',
                f'button[data-time="{duration}"]',
                f'button[data-seconds="{duration}"]',
                f'button[data-minutes="{duration_minutes}"]' if duration_minutes > 0 else None,
                f'button[value="{duration}"]',

                # Class-based selectors
                '.duration-button',
                '.time-button',
                '.expiry-button',
                '.timer-button',
                '.time-selector button',
                '.duration-selector button',
                '.expiry-selector button',

                # Generic selectors
                '.trading-panel button[data-duration]',
                '.trade-panel button[data-time]',
                '.timer-panel button',
            ]

            # Add text-based selectors for each duration format
            for text in duration_text_options:
                duration_selectors.extend([
                    f'button:has-text("{text}")',
                    f'button:has-text("{text.upper()}")',
                    f'button:has-text("{text.lower()}")',
                ])

            # Remove None values
            duration_selectors = [sel for sel in duration_selectors if sel is not None]

            for selector in duration_selectors:
                try:
                    print(f"🔍 Trying duration selector: {selector}")
                    duration_button = await self.page.wait_for_selector(selector, timeout=2000)
                    if duration_button and await duration_button.is_visible():
                        await duration_button.click()
                        await asyncio.sleep(1)
                        print(f"✅ Successfully set duration: {duration}s")
                        return True
                except Exception as e:
                    print(f"⚠️ Duration selector {selector} failed: {e}")
                    continue

            # Try dropdown/select approach
            print(f"🔍 Looking for duration dropdown...")
            dropdown_selectors = [
                'select[name="duration"]',
                'select[name="time"]',
                'select[name="expiry"]',
                '.duration-select',
                '.time-select',
                '.expiry-select'
            ]

            for selector in dropdown_selectors:
                try:
                    dropdown = await self.page.wait_for_selector(selector, timeout=2000)
                    if dropdown and await dropdown.is_visible():
                        await dropdown.select_option(value=str(duration))
                        await asyncio.sleep(1)
                        print(f"✅ Set duration via dropdown: {duration}s")
                        return True
                except:
                    continue

            print(f"⚠️ Duration selector not found, using default duration")
            return True

        except Exception as e:
            print(f"❌ Duration setting error: {e}")
            return False

    async def _click_trade_button(self, action):
        """INSTANT trade button click using cached selectors"""
        try:
            print(f"⚡ INSTANT {action.upper()} button click...")

            # FIRST: Try cached selectors (FASTEST)
            cached_success = await self._quick_selector_click(action)
            if cached_success:
                return True

            # FALLBACK: Try enhanced selectors if cached fails
            print(f"⚠️ Cached selectors failed, trying enhanced search...")

            if action.lower() == 'call':
                # Enhanced CALL/HIGHER/UP button selectors
                call_selectors = [
                    # Common button selectors
                    'button[data-direction="call"]',
                    'button[data-direction="up"]',
                    'button[data-direction="higher"]',
                    'button[data-testid="call-button"]',
                    'button[data-testid="higher-button"]',
                    'button[data-testid="up-button"]',

                    # Text-based selectors
                    'button:has-text("CALL")',
                    'button:has-text("Call")',
                    'button:has-text("HIGHER")',
                    'button:has-text("Higher")',
                    'button:has-text("UP")',
                    'button:has-text("Up")',
                    'button:has-text("ACIMA")',  # Portuguese
                    'button:has-text("Acima")',

                    # Class-based selectors
                    '.call-button',
                    '.higher-button',
                    '.up-button',
                    '.btn-call',
                    '.btn-higher',
                    '.btn-up',
                    '.trade-call',
                    '.trade-higher',

                    # Color-based (green buttons typically for CALL)
                    'button[style*="green"]',
                    'button[class*="green"]',
                    '.green-button',
                    '.btn-success',

                    # Generic trading buttons
                    '.trading-button:first-child',
                    '.trade-buttons button:first-child',
                    '.option-buttons button:first-child'
                ]

                for selector in call_selectors:
                    try:
                        print(f"🔍 Trying selector: {selector}")
                        call_button = await self.page.wait_for_selector(selector, timeout=2000)
                        if call_button and await call_button.is_visible():
                            await call_button.click()
                            await asyncio.sleep(3)  # Wait longer for trade to process
                            print(f"✅ Successfully clicked CALL button with selector: {selector}")
                            return True
                    except Exception as e:
                        print(f"⚠️ Selector {selector} failed: {e}")
                        continue

            else:  # PUT
                # Enhanced PUT/LOWER/DOWN button selectors
                put_selectors = [
                    # Common button selectors
                    'button[data-direction="put"]',
                    'button[data-direction="down"]',
                    'button[data-direction="lower"]',
                    'button[data-testid="put-button"]',
                    'button[data-testid="lower-button"]',
                    'button[data-testid="down-button"]',

                    # Text-based selectors
                    'button:has-text("PUT")',
                    'button:has-text("Put")',
                    'button:has-text("LOWER")',
                    'button:has-text("Lower")',
                    'button:has-text("DOWN")',
                    'button:has-text("Down")',
                    'button:has-text("ABAIXO")',  # Portuguese
                    'button:has-text("Abaixo")',

                    # Class-based selectors
                    '.put-button',
                    '.lower-button',
                    '.down-button',
                    '.btn-put',
                    '.btn-lower',
                    '.btn-down',
                    '.trade-put',
                    '.trade-lower',

                    # Color-based (red buttons typically for PUT)
                    'button[style*="red"]',
                    'button[class*="red"]',
                    '.red-button',
                    '.btn-danger',

                    # Generic trading buttons
                    '.trading-button:last-child',
                    '.trade-buttons button:last-child',
                    '.option-buttons button:last-child'
                ]

                for selector in put_selectors:
                    try:
                        print(f"🔍 Trying selector: {selector}")
                        put_button = await self.page.wait_for_selector(selector, timeout=2000)
                        if put_button and await put_button.is_visible():
                            await put_button.click()
                            await asyncio.sleep(3)  # Wait longer for trade to process
                            print(f"✅ Successfully clicked PUT button with selector: {selector}")
                            return True
                    except Exception as e:
                        print(f"⚠️ Selector {selector} failed: {e}")
                        continue

            print(f"❌ {action.upper()} button not found with any selector")

            # Try to take a screenshot for debugging
            try:
                await self.page.screenshot(path=f"trade_button_debug_{action}.png")
                print(f"📸 Screenshot saved for debugging: trade_button_debug_{action}.png")
            except:
                pass

            return False

        except Exception as e:
            print(f"❌ Trade button click error: {e}")
            return False
    
    def check_asset_open(self, asset):
        """Check if asset is available for trading"""
        # For OTC pairs, they're available 24/7
        if "_otc" in asset:
            return (asset, True, True)  # (asset_name, exists, is_open)
        else:
            # For live pairs, assume they're available during market hours
            return (asset, True, True)
    
    @property
    def check_connect(self):
        """Check if connected"""
        return self.is_authenticated
    
    async def close(self):
        """Close connection and cleanup browser gracefully"""
        try:
            self.is_authenticated = False
            self.websocket_connected = False

            # Silent cleanup to prevent error messages on Ctrl+C
            if self.page:
                try:
                    await self.page.close()
                except:
                    pass  # Silent cleanup
                self.page = None

            if self.context:
                try:
                    await self.context.close()
                except:
                    pass  # Silent cleanup
                self.context = None

            if self.browser:
                try:
                    await self.browser.close()
                except:
                    pass  # Silent cleanup
                self.browser = None

        except Exception as e:
            pass  # Silent cleanup - no error messages on shutdown

# Global instance
quotex_integration = None

def get_quotex_client(email, password, demo_mode=True):
    """Get or create Quotex client instance"""
    global quotex_integration
    
    if quotex_integration is None:
        quotex_integration = QuotexBotIntegration(email, password, demo_mode)
    
    return quotex_integration
